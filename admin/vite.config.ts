import tsconfigPaths from "vite-tsconfig-paths";
import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";

export default ({ mode }: { mode: string }) => {
  const env = loadEnv(mode, process.cwd());

  return defineConfig({
    plugins: [
      react({
        jsxImportSource: "@emotion/react",
        babel: {
          plugins: ["@emotion/babel-plugin"],
        },
      }),
      tsconfigPaths(),
    ],
    // To silence the warnings about 'this'
    esbuild: {
      logOverride: { "this-is-undefined-in-esm": "silent" },
    },
    optimizeDeps: {
      include: ["react/jsx-runtime"],
    },
    build: {
      minify: false,
      reportCompressedSize: false,
    },
    server: {
      port: parseInt(env.VITE_PORT, 10) || 3000,
      proxy: {
        // '/api/v1/giphy-api': {
        //   target: 'https://api.giphy.com/v1',
        //   changeOrigin: true,
        //   rewrite: (path) => path.replace(/^\/api\/v1\/giphy-api/, '')
        // },
        "/api": {
          target: env.VITE_API_URI || "http://localhost:5030",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ""),
        },
      },
    },
  });
};
