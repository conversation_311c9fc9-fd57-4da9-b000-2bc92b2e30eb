import DarkModeIcon from "@mui/icons-material/DarkModeRounded";
import LightModeIcon from "@mui/icons-material/LightModeRounded";
import IconButton, { IconButtonOwnProps } from "@mui/material/IconButton";
import { useColorScheme } from "@mui/material/styles";

export default function ColorModeButton(props: IconButtonOwnProps) {
  const { mode, systemMode, setMode } = useColorScheme();
  const resolvedMode = (systemMode || mode) as "light" | "dark";

  const toggleMode = () => {
    const newMode = resolvedMode === "dark" ? "light" : "dark";
    setMode(newMode);
  };

  const icon = {
    light: <LightModeIcon />,
    dark: <DarkModeIcon />,
  }[resolvedMode];

  return (
    <IconButton
      data-screenshot="toggle-mode"
      onClick={toggleMode}
      disableRipple
      size="medium"
      {...props}
    >
      {icon}
    </IconButton>
  );
}
