import {
  Controller,
  ControllerRenderProps,
  FieldValues,
  useFormContext,
  useFormState,
} from "react-hook-form";
import {
  Box,
  FormControl,
  FormLabel,
  Stack,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
// import { useTheme } from "src/hooks";
// import { useCheckIfLocked } from "src/hooks/useCheckIfLocked";
// import { UserSubscription } from "src/models/user";
// import { blockedDialogState } from "src/recoil/atoms/dialog";
import { MuiTelInput } from "mui-tel-input";
import { TInputProps } from "./types";

export const Input = ({
  name,
  label,
  helperText,
  type,
  maskFn,
  tooltip,
  step,
  min,
  max,
  onValueChange,
  ...other
}: TInputProps) => {
  const { setValue } = useFormContext();
  const { errors } = useFormState();
  // const theme = useTheme();
  const errorMessage = errors[name]?.message as string;
  // const primaryColor = theme.palette.primary.main;
  // const errorColor = theme.palette.error.main;
  // const borderColor = !!errorMessage ? errorColor : primaryColor;
  // const { checkIfLocked } = useCheckIfLocked();

  const phoneInput = (field: ControllerRenderProps<FieldValues, string>) => {
    return (
      <Box
        sx={{
          "&>div>.form-control": {
            width: "100%",
            borderRadius: "4px",
            background: "transparent",
            // "&:focus": {
            //   boxShadow: `0 0 0 1px ${borderColor}`,
            //   borderColor,
            // },
          },
          "&>div>.special-label": {
            top: "-10px",
            left: "10px",
          },
        }}
      >
        <MuiTelInput
          {...field}
          fullWidth
          defaultCountry="AM"
          // country={"us"}
          // isValid={!errorMessage}
        />
        {/* {errorMessage && (
        <Typography variant="caption" color="error" paddingLeft={2}>
          {errorMessage}
        </Typography>
      )} */}
      </Box>
    );
  };

  const textInput = (field: ControllerRenderProps<FieldValues, string>) => (
    <FormControl>
      <FormLabel htmlFor={field.name}>{label}</FormLabel>

      <TextField
        {...field}
        variant="outlined"
        fullWidth
        onChange={(event) => {
          field.onChange(event);
          if (onValueChange) {
            onValueChange(event.target.value);
          }
          if (maskFn) {
            setValue(name, maskFn(event.target.value));
          }
        }}
        error={!!errorMessage}
        helperText={errorMessage || helperText}
        type={type}
        {...other}
      />
    </FormControl>
  );

  return (
    <Controller
      name={name}
      render={({ field }) => (
        <>
          {type === "tel" ? (
            phoneInput(field)
          ) : (
            <>
              {tooltip ? (
                <Stack direction="row" width="100%">
                  {textInput(field)}
                  {/* <Tooltip sx={{ ml: 1 }} title={tooltip} placement="top">
                    <FAIcon fontSize={IconSize.Small} icon={faInfoCircle} />
                  </Tooltip> */}
                </Stack>
              ) : (
                textInput(field)
              )}
            </>
          )}
        </>
      )}
    />
  );
};
