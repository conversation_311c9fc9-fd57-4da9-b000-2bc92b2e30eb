import { useState } from "react";

import { Input } from "../input/Input";
import { TInputProps } from "./types";
import { IconButton } from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";

export const PasswordInput = ({
  name = "password",
  label = "Password",
  autoComplete = "current-password",
  ...other
}: TInputProps) => {
  const [showPassword, setShowPassword] = useState(false);

  return (
    <Input
      name={name}
      type={showPassword ? "text" : "password"}
      label={label}
      autoComplete={autoComplete}
      slotProps={{
        input: {
          endAdornment: (
            <IconButton onClick={() => setShowPassword((prev) => !prev)}>
              {showPassword ? <Visibility /> : <VisibilityOff />}
            </IconButton>
          ),
        },
      }}
      {...other}
    />
  );
};
