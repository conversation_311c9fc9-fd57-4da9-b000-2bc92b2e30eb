import { useMemo } from "react";
// import { useLocales } from 'src/hooks';
import { TCountry } from "src/models/country";
import { useCountries } from "src/queries/common";
import { transformToSelectData } from "src/transformers/common";

import Select from "../select";

export const CountrySelect = ({
  name = "countryId",
  optionKey = "id",
  value = "nicename",
}: {
  name?: string;
  optionKey?: keyof TCountry;
  value?: keyof TCountry;
}) => {
  const { data: countriesData } = useCountries();

  // const { t } = useLocales();

  const countries = useMemo(
    () =>
      (countriesData || []).map((item) =>
        transformToSelectData.createOption(item[optionKey], item[value])
      ),
    [countriesData, optionKey, value]
  );

  return <Select name={name} options={countries} label={"Country"} />;
};
