// import { faArrowLeft, FAIcon, faTimes } from "src/components/icons";
import {
  <PERSON>,
  Button,
  Divider,
  Drawer,
  Icon<PERSON>utton,
  Stack,
  Tooltip,
  Typography,
  Switch,
} from "@mui/material";
// import { VIEWER_DISABLED } from "src/config";
// import { useBoolean, useLocales } from "src/hooks";
import { useBoolean } from "usehooks-ts";
// import AlertDialog from "../alert-dialog";
import { DrawerWidth, TEditDrawerProps } from "./types";
import { APPBAR_HEIGHT } from "../dashboard-layout/config";

export const EDIT_DRAWER_BOTTOM = "edit-drawer-bottom";
export const EDIT_DRAWER_TITLE = "edit-drawer-title";

export function EditDrawer(props: TEditDrawerProps) {
  const {
    anchor = "right",
    width = DrawerWidth.MD,
    title,
    isEnabled,
    saveLabel = "save",
    deleteLabel = "delete",
    overflowY = "auto",
    overflowX = "auto",
    formId,
    hasChanged = false,
    disableDelete,
    deleteButtonTooltip,
    children,
    cancelButton = true,
    cancelLabel,
    contentContainerStyles,
    activeOutside,
    collapse,
    pb = 12,
    contentRef,
    onGoBack,
    onClose,
    onDelete,
    onReset,
    onCancel,
    onSave,
    onEnabledChanged,
    sx,
    deleteButtonOnLeft,
    icon,
    enableSwitch,
    switchValue,
    onSwitch,
    ...rest
  } = props;
  //   const { t } = useLocales();
  const {
    value: isAlertVisible,
    setTrue: showAlert,
    setFalse: closeAlert,
  } = useBoolean();

  const handleClose = () => {
    if (hasChanged) {
      showAlert();
      return;
    }

    onClose();
  };

  const handleCancel = () => {
    onCancel?.();
    onClose();
    closeAlert();
  };

  const handleSave = () => {
    onSave?.();
  };

  return (
    <>
      <Drawer
        disableEnforceFocus
        anchor={anchor}
        SlideProps={{
          direction: "left",
        }}
        onClose={handleClose}
        id="edit-drawer"
        sx={{
          ...(activeOutside
            ? {
                "&>.MuiPaper-root": {
                  height: `calc(100vh - ${APPBAR_HEIGHT})`,
                  top: APPBAR_HEIGHT,
                },
              }
            : {}),
          ...sx,
        }}
        {...rest}
        {...(collapse ? { open: false } : {})}
      >
        <Box width={width} maxWidth="90vw" overflow="hidden">
          <Box position="sticky" top="0">
            <Stack
              direction="row"
              justifyContent="space-between"
              alignItems="center"
              paddingX={2}
              paddingY={1}
            >
              <Stack
                direction="row"
                alignItems="center"
                spacing={2}
                flexGrow={1}
                maxWidth="80%"
                overflow="hidden"
              >
                {!!onGoBack && (
                  <IconButton size="large" onClick={onGoBack}>
                    {/* <FAIcon icon={faArrowLeft} /> */}
                    arrow left
                  </IconButton>
                )}
                {icon && (
                  <Stack
                    sx={{
                      borderRadius: "8px",
                      background: "rgba(32, 85, 255, 0.06)",
                      p: "4px",
                    }}
                  >
                    {icon}
                  </Stack>
                )}
                {title ? (
                  <Typography variant="h6" noWrap>
                    {title}
                  </Typography>
                ) : (
                  <Box id={EDIT_DRAWER_TITLE} flexGrow={1} />
                )}
              </Stack>
              {enableSwitch && onSwitch && (
                <Switch checked={switchValue} onChange={onSwitch} />
              )}
              <IconButton size="large" onClick={handleClose}>
                {/* <FAIcon icon={faTimes} /> */}
                fa times
              </IconButton>
            </Stack>
            <Divider />
          </Box>
          <Box
            ref={contentRef}
            sx={{
              overflowY,
              overflowX,
              height: `calc(100vh - ${activeOutside ? 177 : 132}px)`,
              padding: 2,
              paddingBottom: pb,
              ...(contentContainerStyles || {}),
            }}
          >
            {children}
          </Box>
          <Box position="fixed" bottom="0" width={width}>
            <Divider />
            <Stack
              direction="row"
              spacing={2}
              padding={2}
              justifyContent="flex-end"
            >
              <Stack direction="row" id={EDIT_DRAWER_BOTTOM} />
              {onDelete &&
                (deleteButtonTooltip ? (
                  <Tooltip title={deleteButtonTooltip}>
                    <Button
                      //   className={VIEWER_DISABLED}
                      onClick={onDelete}
                      disabled={disableDelete}
                    >
                      {deleteLabel}
                    </Button>
                  </Tooltip>
                ) : (
                  <Button
                    // className={VIEWER_DISABLED}
                    onClick={onDelete}
                    disabled={disableDelete}
                    sx={{
                      marginRight: deleteButtonOnLeft
                        ? "auto !important"
                        : "unset",
                    }}
                  >
                    {deleteLabel}
                  </Button>
                ))}
              {onReset && (
                <Button onClick={onReset}>
                  reset
                  {/* {t("button.reset")} */}
                </Button>
              )}
              {cancelButton && (
                <Button variant="outlined" onClick={handleCancel}>
                  {/* {cancelLabel || t("button.cancel")} */}
                  cancel
                </Button>
              )}

              {!!saveLabel && (
                <Button
                  variant="contained"
                  type={formId ? "submit" : "button"}
                  form={formId}
                  onClick={handleSave}
                  disabled={!hasChanged}
                >
                  {saveLabel}
                </Button>
              )}
            </Stack>
          </Box>
        </Box>
      </Drawer>
      {/* {isAlertVisible && (
        <AlertDialog
          open={isAlertVisible}
          onCancel={closeAlert}
          onAgree={handleCancel}
        >
          <Typography variant="body1" textAlign="center">
            {t("home.areYouSureMessage")}
          </Typography>
        </AlertDialog>
      )} */}
    </>
  );
}
