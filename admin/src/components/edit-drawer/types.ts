import { DrawerProps } from "@mui/material";
import { ReactElement } from "react";

export enum DrawerWidth {
  SM = "300px",
  MD = "400px",
  MDL = "435px",
  LG = "600px",
  XL = "800px",
  XXL = "90vw",
}

export type TEditDrawerProps = DrawerProps & {
  width?: React.CSSProperties["width"];
  title?: string | React.ReactNode;
  isEnabled?: boolean;
  saveLabel?: string | null;
  deleteLabel?: string;
  formId?: string;
  hasChanged?: boolean;
  overflowY?: React.CSSProperties["overflow"];
  overflowX?: React.CSSProperties["overflow"];
  disableDelete?: boolean;
  deleteButtonTooltip?: string;
  cancelButton?: boolean;
  cancelLabel?: string;
  contentContainerStyles?: any;
  // Don't close the drawer when clicking outside of it, and don't show the backdrop
  activeOutside?: boolean;
  // To collapse the drawer while keeping it mounted
  collapse?: boolean;
  pb?: number;
  contentRef?: (node: HTMLDivElement | null) => void;
  onClose: () => void;
  onDelete?: () => void;
  onReset?: () => void;
  onCancel?: () => void;
  onSave?: () => void;
  onGoBack?: () => void;
  onTitleChange?: (title: string) => void;
  onEnabledChanged?: (enabled: boolean) => void;
  deleteButtonOnLeft?: boolean;
  icon?: ReactElement;
  enableSwitch?: boolean;
  switchValue?: boolean;
  onSwitch?: () => void;
};
