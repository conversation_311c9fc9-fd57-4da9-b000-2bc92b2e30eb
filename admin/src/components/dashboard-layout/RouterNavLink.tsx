import { styled } from "@mui/material/styles";
import { NavLink } from "react-router-dom";

interface IRouterLinkProps {
  to?: string;
  end?: boolean;
}

export const RouterNavLink = styled(NavLink)<IRouterLinkProps>(({ theme }) => ({
  ...theme.typography.subtitle2,
  color: theme.palette.text.primary,
  // marginRight: theme.spacing(2),
  textDecoration: "none",
  transition: theme.transitions.create("opacity", {
    duration: theme.transitions.duration.shortest,
  }),
  "&:hover": {
    opacity: 0.48,
    cursor: "pointer",
    textDecoration: "none",
  },
  "&.active": {
    background: theme.palette.background.paper,
  },
}));
