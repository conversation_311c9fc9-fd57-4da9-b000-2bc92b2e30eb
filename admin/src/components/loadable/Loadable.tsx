import { CircularProgress } from "@mui/material";
import { Suspense } from "react";

export const Loadable = (Component: React.ElementType) => (props: unknown) => {
  return (
    <Suspense
      fallback={
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "100vh",
          }}
        >
          <CircularProgress />
        </div>
      }
    >
      <Component {...(props as object)} />
    </Suspense>
  );
};
