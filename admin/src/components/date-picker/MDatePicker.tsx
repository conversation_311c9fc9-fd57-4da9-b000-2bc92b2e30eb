import { DatePicker } from "@mui/x-date-pickers";
import dayjs, { Dayjs } from "dayjs";
import { useAtom } from "jotai";
import { dateLabel } from "src/atoms/dashboard";
import { DatesPeriod } from "src/types/dates";

interface ShortcutItem {
  label: string;
  name: keyof typeof DatesPeriod;
  getValue: () => Dayjs | null;
}

const shortcutsItems: ShortcutItem[] = [
  {
    label: "This Week",
    name: "current_week",
    getValue: () => {
      const today = dayjs();
      return today.startOf("week");
    },
  },
  {
    label: "Last Week",
    name: "last_week",
    getValue: () => {
      const today = dayjs();
      const prevWeek = today.subtract(7, "day");
      return prevWeek.startOf("week");
    },
  },
  {
    label: "Last 7 Days",
    name: "last_7_days",
    getValue: () => {
      const today = dayjs();
      return today.subtract(7, "day");
    },
  },
  {
    label: "Current Month",
    name: "current_month",
    getValue: () => {
      const today = dayjs();
      return today.startOf("month");
    },
  },
  {
    label: "Next Month",
    name: "next_month",
    getValue: () => {
      const today = dayjs();
      const startOfNextMonth = today.endOf("month").add(1, "day");
      return startOfNextMonth;
    },
  },
  { label: "Reset", getValue: () => null, name: "current_month" },
];

export const MDatePicker = () => {
  const [value, setValue] = useAtom(dateLabel);

  return (
    <DatePicker
      label="Controlled picker"
      selectedSections={"day"}
      //   defaultValue={dayjs()}
      closeOnSelect={false}
      //   value={value}
      onChange={(_, { shortcut }) => {
        if (shortcut && (shortcut as ShortcutItem)?.name)
          setValue((shortcut as ShortcutItem).name);
      }}
      slotProps={{
        shortcuts: {
          items: shortcutsItems,
        },
        textField: {
          inputProps: {
            value: DatesPeriod[value],
          },
        },
      }}
    />
  );
};
