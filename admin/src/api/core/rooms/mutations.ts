import { RoomsAPI } from ".";
import { Queries } from "./types";
// import { AuthAPI } from ".";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export function useMutateRooms() {
  // const queryClient = useQueryClient();

  const { mutateAsync: upsertRoomCategory } = useMutation({
    mutationKey: [Queries.QUERY, Queries.GET_CATEGORIES],
    mutationFn: async (data) => {
      const response = await RoomsAPI.addRoomCategory({ data });
      return response.data;
    },
    onSuccess: (data) => {},
  });

  return {
    upsertRoomCategory,
  };
}
