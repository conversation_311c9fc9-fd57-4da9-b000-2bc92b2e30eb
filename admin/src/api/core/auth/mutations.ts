import { Queries } from "./types";
import { AuthAPI } from ".";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export function useAuthRequests({
  removeUserLogged,
}: {
  removeUserLogged: () => void;
}) {
  const queryClient = useQueryClient();

  const { mutateAsync: signup } = useMutation({
    mutationKey: [Queries.QUERY, Queries.SIGNUP],
    mutationFn: async (data) => {
      const response = await AuthAPI.signup({ data });
      return response.data;
    },
    onSuccess: (data) => {},
  });

  const { mutateAsync: loginMutation } = useMutation({
    mutationKey: [Queries.QUERY, Queries.LOGIN],
    mutationFn: async (data: any) => {
      const response = await AuthAPI.login({ data });
      return response.data;
    },
    onSuccess: (data) => {},
  });

  const { mutateAsync: googleLoginMutation } = useMutation({
    mutationKey: [Queries.QUERY, Queries.LOGIN],
    mutationFn: async (data: any) => {
      const response = await AuthAPI.googleLogin({ data });
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.setQueryData([Queries.QUERY, Queries.GET_USER], data.user);
    },
  });

  const { mutateAsync: logout } = useMutation({
    mutationKey: [Queries.QUERY, Queries.LOGIN],
    mutationFn: async () => {
      const response = await AuthAPI.logout();
      return response.data;
    },
    onSuccess: () => {
      queryClient.setQueryData([Queries.QUERY, Queries.GET_USER], null);
      removeUserLogged();
    },
  });

  return {
    signup,
    loginMutation,
    googleLoginMutation,
    logout,
  };
}
