import API from "../base";

class AuthAPI extends API {
  public getAll() {
    return {
      getUser: (options = {}) => this.get({ url: "/auth/me", ...options }),

      signup: ({ ...options } = {}) =>
        this.post({ url: "auth/signup", ...options }),

      login: ({ ...options } = {}) =>
        this.post({ url: "auth/login", ...options }),

      googleLogin: ({ ...options } = {}) =>
        this.post({ url: "auth/google", ...options }),

      logout: ({ ...options } = {}) =>
        this.post({ url: "auth/logout", ...options }),

      confirmEmail: ({ ...options } = {}) =>
        this.post({ url: "auth/confirmEmail", ...options }),

      resetPassword: ({ ...options } = {}) =>
        this.post({ url: "auth/resetPassword", ...options }),

      forgotPassword: ({ ...options } = {}) =>
        this.post({ url: "auth/forgotPassword", ...options }),
    };
  }
}

const authApi = new AuthAPI().getAll();

export { authApi as AuthAPI };
