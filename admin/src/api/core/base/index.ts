import { AxiosResponse } from "axios";
// import { cloneDeep } from "lodash";
// import { HttpStatusCode } from 'src/api/type';
import { getErrorMessage, toQueryString } from "src/utils";

import axios from "./axios-instance";
import { HttpStatusCode, RequestMethod } from "./types";

const handleResponse = (result: AxiosResponse) => {
  if (result.status === HttpStatusCode.SUCCESS) {
    return result.data;
  } else {
    const errorMessage = getErrorMessage(result);
    throw new Error(errorMessage, { cause: result.status });
  }
};

export default class API {
  public async get({ url = "", ...rest }) {
    const config = rest.config ? rest.config : {};

    if (rest.query) {
      url += `?${toQueryString(rest.query)}`;
    }
    const results = await axios(
      url,
      Object.assign(
        {
          method: RequestMethod.GET,
          ...rest,
        },
        config
      )
    );

    return handleResponse(results);
  }

  public async post({ url = "", ...rest }) {
    /** NOTE: FormData can be passed only throw rest.data parameter */
    const data = rest.data ? rest.data : rest;
    const isFormData = rest.data instanceof FormData;
    let body;
    const config = rest.config ? rest.config : {};

    if (isFormData) {
      const headers = { ...(config.headers || {}) };
      headers["Content-Type"] = "multipart/form-data";
      config.headers = headers;
      body = rest.data;
    } else {
      body = JSON.stringify(data);
    }

    const results = await axios(
      url,
      Object.assign(
        {
          method: RequestMethod.POST,
          ...rest,
        },
        { body },
        config
      )
    );

    return handleResponse(results);
  }

  public async put({ url = "", ...rest }) {
    /** NOTE: FormData can be passed only throw rest.data parameter */
    const data = rest?.data || rest;
    const isFormData = rest.data instanceof FormData;

    let body;
    const config = rest?.config || {};

    if (isFormData) {
      const headers = { ...(config.headers || {}) };

      headers["Content-Type"] = "multipart/form-data";
      config.headers = headers;
      body = rest.data;
    } else {
      body = JSON.stringify(data);
    }

    const results = await axios(
      url,
      Object.assign(
        {
          method: RequestMethod.PUT,
          ...rest,
        },
        { body },
        config
      )
    );

    return handleResponse(results);
  }

  public async patch({ url = "", ...rest }) {
    /** NOTE: FormData can be passed only throw rest.data parameter */
    const data = rest?.data || rest;
    const isFormData = rest.data instanceof FormData;

    let body;
    const config = rest?.config || {};

    if (isFormData) {
      const headers = { ...(config.headers || {}) };

      headers["Content-Type"] = "multipart/form-data";
      config.headers = headers;
      body = rest.data;
    } else {
      body = JSON.stringify(data);
    }

    const results = await axios(
      url,
      Object.assign(
        {
          method: RequestMethod.PATCH,
          ...rest,
        },
        { body },
        config
      )
    );

    return handleResponse(results);
  }

  public async delete({ url = "", ...rest }) {
    const config = rest?.config || {};
    config.headers = { ...(config.headers || {}) };

    const data = rest?.data || rest;
    const body = JSON.stringify(data);

    const results = await axios(
      url,
      Object.assign(
        {
          method: RequestMethod.DELETE,
          ...rest,
        },
        { body },
        config
      )
    );

    return handleResponse(results);
  }
}
