import axios from "axios";
import {
  request,
  requestError,
  handleResponse,
  responseError,
} from "src/api/core/base/axios-instance/interceptor";

const instance = axios.create({
  baseURL: `${
    import.meta.env.VITE_APP_NODE_ENV == "local"
      ? "http://localhost:3000/api"
      : import.meta.env.VITE_APP_DOMAIN
  }/${import.meta.env.VITE_API_VERSION || "v1"}/`,
  //0 and false it accepts as true, so there is check
  withCredentials:
    import.meta.env.VITE_AXIOS_WITH_CREDENTIALS != 0 &&
    import.meta.env.VITE_AXIOS_WITH_CREDENTIALS != "false",
});

instance.interceptors.request.use(request, requestError);

// instance.interceptors.response.use(handleResponse, responseError);

export default instance;
