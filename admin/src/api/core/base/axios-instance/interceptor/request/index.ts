import { AxiosError, InternalAxiosRequestConfig } from "axios";
// import { localStorage } from 'src/utils';

export const request = (config: InternalAxiosRequestConfig) => {
  // const { get: getFingerprint } = localStorage('fingerprint');

  const fingerprint = ""; // getFingerprint() || "";

  if (config?.headers) {
    if (fingerprint) {
      config.headers["X-Device-Fingerprint"] = String(fingerprint);
    }

    config.withCredentials = true;
  }

  return config;
};

export const requestError = (error: AxiosError) => Promise.reject(error);
