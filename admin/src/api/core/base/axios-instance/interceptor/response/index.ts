import { AxiosError, AxiosResponse } from "axios";
import { Code } from "src/api/code";
// import { localStorage } from "src/utils";

import { CoreApiResponse } from "./CoreApiResponse";

export const handleResponse = <T>(
  response: AxiosResponse<CoreApiResponse<T>>
) => {
  if (
    !response.data.status &&
    response.headers["content-type"]?.startsWith("application/json")
  ) {
    const messageProps = "message";
    let responseData: Record<string, unknown> = {};
    const entries = Object.entries(response.data);

    entries.forEach(([key, value]) => {
      if (key === messageProps) {
        return;
      }
      if (entries.length < 3) {
        responseData = value;
      } else {
        responseData[key] = value;
      }
    });
    return {
      ...response,
      data: CoreApiResponse.success(responseData),
      message: response.data.message,
    };
  }
  return response;
};

export const responseError = (error: AxiosError<CoreApiResponse<null>>) => {
  if (!error?.response?.data?.status) {
    return CoreApiResponse.error(
      error.response?.status,
      error?.response?.data?.message
    );
  }

  return (
    error?.response?.data ||
    CoreApiResponse.error(Code.BAD_REQUEST_ERROR.code, error?.message)
  );
};
