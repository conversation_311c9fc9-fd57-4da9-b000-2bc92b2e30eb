export type TError = {
  code: number;
  status: number;
  data?: Array<Array<string>>;
  message: string;
};

export type THeader = {
  ["x-csrf-token"]?: string;
  ["X-Device-Fingerprint"]?: string;
  Authorization?: string;
};

export enum RequestMethod {
  GET = "get",
  DELETE = "delete",
  HEAD = "head",
  OPTIONS = "options",
  POST = "post",
  PUT = "put",
  PATCH = "patch",
  PURGE = "purge",
  LINK = "link",
  UNLINK = "unlink",
}

export enum HttpStatusCode {
  SUCCESS = 200,
  BAD_REQUEST_ERROR = 400,
  UNAUTHORIZED_ERROR = 401,
  WRONG_CREDENTIALS_ERROR = 402,
  ACCESS_DENIED_ERROR = 403,
  VALIDATION_ERROR = 422,
  NOT_FOUND = 404,
  INTERNAL_ERROR = 500,
}
