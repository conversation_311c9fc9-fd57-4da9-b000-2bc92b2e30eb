import { createRoot } from "react-dom/client";
import "./index.css";
import { <PERSON><PERSON><PERSON><PERSON>out<PERSON> } from "react-router-dom";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import App from "./App";

import { GoogleOAuthProvider } from "@react-oauth/google";

createRoot(document.getElementById("root")!).render(
  <BrowserRouter>
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <GoogleOAuthProvider clientId="548781374180-5qs78v5st7l2ktlvjaf8006m16q3mhba.apps.googleusercontent.com">
        <App />
      </GoogleOAuthProvider>
    </LocalizationProvider>
  </BrowserRouter>
);
