import { useAtomValue } from "jotai";
import { dateLabel } from "src/atoms/dashboard";
import dayjs from "dayjs";
import { useMemo } from "react";

const today = dayjs();

export const datelabelToDate = {
  current_month: {
    start: today
      // .startOf("month")
      .subtract(5, "day"),
    length: today.daysInMonth(),
  },
  current_week: {
    start: today.startOf("week"),
    length: 7,
  },
  last_week: {
    start: today.subtract(1, "week").startOf("week"),
    length: 7,
  },
  last_7_days: {
    start: today.subtract(7, "day"),
    length: 7,
  },
  next_month: {
    start: today.endOf("month").add(1, "day"),
    length: today.endOf("month").add(1, "day").daysInMonth(),
  },
};

// TODO: replace with tanstack table columns
export const useColumns = () => {
  const dateValue = useAtomValue(dateLabel);
  const columns: GridColDef[] = useMemo(
    () =>
      Array.from({ length: datelabelToDate[dateValue]?.length }, (_, i) => {
        const date = datelabelToDate[dateValue]?.start;
        const updatedDate = date.add(i, "day");
        const formattedDate = updatedDate.format("MMM DD");
        const idDate = updatedDate.format("YYYY-MM-DD");

        return {
          // field: `day${i}`,
          index: i,
          id: idDate, //i, // TODO replace index with date
          key: i,
          date: updatedDate,
          headerName: formattedDate,
          flex: 1,
        };
      }),
    [dateValue]
  );

  const datesRangeArray = useMemo(() => {
    const startDate = today.subtract(2, "years"); // columns[0]?.id,
    const endDate = today.add(2, "years"); //columns[columns.length - 1]?.id,
    const daysCount = endDate.diff(startDate, "day");

    const array = Array.from({ length: daysCount }, (_, i) => {
      const date = dayjs(startDate);
      const updatedDate = date.add(i, "day");
      const formattedDate = updatedDate.format("MMM DD");
      const idDate = updatedDate.format("YYYY-MM-DD");
      const yearDate = updatedDate.format("YYYY");
      return {
        id: idDate,
        date: updatedDate,
        headerName: formattedDate,
        year: yearDate,
      };
    });
    return array;
  }, []);

  return { columns, datesRangeArray };
};
