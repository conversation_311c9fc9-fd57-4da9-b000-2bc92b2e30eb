import { useState } from "react";
import { Stack } from "@mui/material";
import { MDatePicker } from "src/components/date-picker/MDatePicker";
import dayjs from "dayjs";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";
import minMax from "dayjs/plugin/minMax";
import { ReservationDrawer } from "./components/reservation-drawer/ReservationDrawer";
import { useSetAtom } from "jotai";
import { resDrawerState } from "src/atoms/reservation-drawer";
import { OverlapDialog } from "./components/overlap-dialog/OverlapDialog";
import { overlapDialogState } from "src/atoms/overlap-dialog";
import Board from "./components/board/Board";

dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);
dayjs.extend(minMax);

export const Dashboard = () => {
  const [isDragging, setIsDragging] = useState(false);
  // const setSelectedReservation = useSetAtom(resDrawerState);
  const setDialogState = useSetAtom(overlapDialogState);

  // const handleOpenDrawer = useCallback((reservation) => {
  //   setSelectedReservation(reservation);
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, []);

  return (
    <Stack p={2}>
      <Stack width="200px" mb={1}>
        <MDatePicker />
      </Stack>
      <Board />
      <ReservationDrawer />
      {/* TODO: bring back overlapping dialog */}
      {/* <OverlapDialog
        setReservations={setReservations}
        setRooms={setRooms}
        setIsDragging={setIsDragging}
      /> */}
    </Stack>
  );

  // function handleDragEnd(event) {
  //   const { over, active } = event;
  //   const activeStartDate = active?.data?.current?.startDate;
  //   const activeEndDate = active?.data?.current?.endDate;
  //   const existingReservation = over?.data?.current?.reservations?.find((r) => {
  //     return (
  //       r.startDate.isSameOrBefore(activeEndDate) &&
  //       r.endDate.isSameOrAfter(activeStartDate)
  //     );
  //   });

  //   if (existingReservation) {
  //     const overlapStart = dayjs.max(
  //       activeStartDate,
  //       existingReservation.startDate
  //     ); // Later of the two start dates
  //     const overlapEnd = dayjs.min(activeEndDate, existingReservation.endDate); // Earlier of the two end dates
  //     // TODO overlap start and end will be needed to split the reservation

  //     setDialogState({
  //       open: true,
  //       roomId: over.id,
  //       existingReservation,
  //       newReservation: active.data.current,
  //     });
  //     // return;
  //   } else {
  //     setIsDragging(false);

  //     if (!over) {
  //       setReservations((prevRes) => {
  //         return prevRes.map((r) => {
  //           if (r.id === active.id) {
  //             return { ...r, room: null };
  //           }
  //           return r;
  //         });
  //       });
  //     }
  //     setRooms((prevRooms) => {
  //       const updatedRows = prevRooms.map((room) => {
  //         if (room.id === over?.id) {
  //           setReservations((prevRes) =>
  //             prevRes.map((r) => {
  //               if (r.id === active.id) {
  //                 return { ...r, room: over.id };
  //               }
  //               return r;
  //             })
  //           );
  //           return {
  //             ...room,
  //             reservations: !room.reservations?.find((r) => r.id === active.id)
  //               ? [...room.reservations, active.data.current]
  //               : [...room.reservations],
  //           };
  //         }
  //         return {
  //           ...room,
  //           reservations: room.reservations?.filter((r) => r.id !== active.id),
  //         };
  //       });
  //       return updatedRows;
  //     });
  //   }
  // }
};
