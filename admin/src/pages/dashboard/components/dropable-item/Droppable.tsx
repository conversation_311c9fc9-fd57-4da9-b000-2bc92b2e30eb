import { useDroppable } from "@dnd-kit/core";
import { Draggable } from "../dragable-item/Draggable";
import { Box, Grid, Stack } from "@mui/material";
import { useColumns } from "../../hooks/useColumns";
import { EasterEgg } from "./EasterEgg";

export function Droppable(props) {
  const { id, reservations, virtualColumns, isCategory, row } = props;
  const { isOver, setNodeRef } = useDroppable({
    id,
    data: { id, reservations, row, isCategory },
  });
  // const { columns } = useColumns();
  const style = {
    backgroundColor: isOver ? "action.hover" : "transparent",
  };

  return (
    <Stack
      flexDirection="row"
      // container
      // display="grid"
      key={id}
      // columns={columns.length}
      // gridTemplateColumns={`repeat(${columns.length}, 1fr)`}
      sx={{
        ...style,
        // borderBottom: 1,
        // borderTop: index === 0 ? 1 : 0,
        height: 50,
        // width: "inherit",
        // "&:hover": { backgroundColor: "action.hover" },
      }}
      ref={setNodeRef}
    >
      <EasterEgg />
      {reservations.map((reservation) => {
        return (
          <Draggable
            key={reservation.id}
            reservation={reservation}
            virtualColumns={virtualColumns}
          />
        );
      })}
    </Stack>
  );
}
