import { Box } from "@mui/material";
import { useEffect, useRef, useState } from "react";
import pony from "src/assets/pony.gif";

export const EasterEgg = () => {
  const [boxPosition, setBoxPosition] = useState(-100); // Initial position: -100px
  const keySequence = useRef(""); // Track the key sequence
  const moving = useRef(false); // Prevent multiple animations

  // Key Event Listener
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (moving.current) return;

      switch (event.key) {
        case "ArrowUp":
          keySequence.current += "↑";
          break;
        case "ArrowDown":
          keySequence.current += "↓";
          break;
        default:
          keySequence.current += event.key;
      }

      // Check the exact sequence
      if (keySequence.current.includes("ac↑b↑ba↓")) {
        moving.current = true;
        animateBox();
        keySequence.current = ""; // Reset after triggering
      }

      // Prevent overflow of keySequence
      if (keySequence.current.length > 10) {
        keySequence.current = keySequence.current.slice(-10);
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, []);

  // Animate the Box across the screen
  const animateBox = () => {
    let currentPosition = -100;
    const interval = setInterval(() => {
      currentPosition += Math.floor(Math.random() * 10) + 1; // 5  ; // Increment position by 5px
      setBoxPosition(currentPosition);

      if (currentPosition >= window.innerWidth) {
        clearInterval(interval);
        moving.current = false; // Allow re-triggering
        setBoxPosition(-100); // Reset position
      }
    }, 16); // Approximately 60fps
  };

  return (
    <Box
      component={"img"}
      src={pony}
      sx={{
        position: "absolute",
        height: "50px",
        left: `${boxPosition}px`,
        transition: "left 0.1s linear",
        visibility: moving.current ? "visible" : "hidden",
        zIndex: 200,
      }}
    />
  );
};
