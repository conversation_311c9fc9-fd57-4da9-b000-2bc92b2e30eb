import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from "@mui/material";
import { useAtom } from "jotai";
import { useCallback } from "react";
import { overlapDialogState } from "src/atoms/overlap-dialog";

// TODO setReservations will be removed and use request instead
export const OverlapDialog = ({ setReservations, setRooms, setIsDragging }) => {
  const [dialogState, setDialogState] = useAtom(overlapDialogState);
  const { open, roomId, existingReservation, newReservation } = dialogState;

  const handleClose = useCallback(() => {
    setDialogState({
      open: false,
      roomId: null,
      existingReservation: null,
      newReservation: null,
    });
    setIsDragging(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleCancelNew = useCallback(() => {
    handleClose();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [newReservation, roomId]);

  const handleCancelExisting = useCallback(() => {
    setRooms((prevRooms) => {
      const updatedRows = prevRooms.map((room) => {
        if (room.id === roomId) {
          setReservations((prevRes) =>
            prevRes.map((r) => {
              if (r.id === newReservation.id) {
                return { ...r, room: roomId };
              }
              if (r.id === existingReservation.id) {
                return { ...r, room: null };
              }
              return r;
            })
          );
          return {
            ...room,
            reservations: !room.reservations?.find(
              (r) => r.id === newReservation.id
            )
              ? [...room.reservations, newReservation].filter(
                  (r) => r.id !== existingReservation.id
                )
              : [...room.reservations],
          };
        }
        return {
          ...room,
          reservations: room.reservations?.filter(
            (r) => r.id !== newReservation.id
          ),
        };
      });
      return updatedRows;
    });
    handleClose();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [existingReservation]);

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <DialogTitle id="alert-dialog-title">
        {"There is a reservation conflict"}
      </DialogTitle>
      <DialogContent>
        <DialogContentText id="alert-dialog-description">
          You can choose between the following options: 1. Cancel the new
          reservation 2. Cancel the existing reservation 3. Split incoming
          reservation
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleCancelNew}>Cancel the new</Button>
        <Button onClick={handleCancelExisting} autoFocus>
          Cancel the existing
        </Button>
        <Button onClick={handleClose} autoFocus>
          Split
        </Button>
      </DialogActions>
    </Dialog>
  );
};
