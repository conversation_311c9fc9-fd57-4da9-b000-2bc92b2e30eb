import { Box, Drawer } from "@mui/material";
import dayjs from "dayjs";
import { useAtom } from "jotai";
import { useCallback } from "react";
import { resDrawerState } from "src/atoms/reservation-drawer";
import { APPBAR_HEIGHT } from "src/components/dashboard-layout/config";

export const ReservationDrawer = () => {
  const [selectedReservation, setSelectedReservation] = useAtom(resDrawerState);
  const handleCloseDrawer = useCallback(() => {
    setSelectedReservation(null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return (
    <Drawer
      anchor={"right"}
      open={!!selectedReservation}
      onClose={handleCloseDrawer}
      sx={{
        "&>.MuiPaper-root": {
          height: `calc(100vh - ${APPBAR_HEIGHT})`,
          top: APPBAR_HEIGHT,
          overflow: "visible",
          borderLeft: "1px solid #E0E0E0",
        },
      }}
    >
      <Box width="40vw" height="100%" role="presentation">
        {selectedReservation?.name}
        {selectedReservation &&
          dayjs(selectedReservation?.startDate).format("YYYY-MM-DD")}
        {selectedReservation &&
          dayjs(selectedReservation?.endDate).format("YYYY-MM-DD")}
      </Box>
    </Drawer>
  );
};
