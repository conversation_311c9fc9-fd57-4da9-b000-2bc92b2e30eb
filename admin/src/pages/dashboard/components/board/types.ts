// Types

export interface DateChunk {
  dates: Array<{
    key: string;
    label: string;
    fullDate: string;
  }>;
  startDate: Date;
  endDate: Date;
}

export interface Room {
  id: number;
  name: string;
  capacity: number;
  reservations: Record<string, unknown>[]; // date -> isBooked
}

export interface RoomCategory {
  id: number;
  name: string;
  rooms: Room[];
  reservations: any[];
  isCategory: true;
}

export type TableRow =
  | RoomCategory
  | (Room & { parentId: string; isCategory?: false });
