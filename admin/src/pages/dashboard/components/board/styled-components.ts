import { Box, styled } from "@mui/material";

export const StyledTableContainer = styled(Box)(({ theme }) => ({
  border: `1px solid ${theme.palette.divider}`,
  borderRadius: theme.shape.borderRadius,
  overflow: "auto",
  height: "600px",
  width: "100%",
}));

export const HeaderCell = styled(Box)(({ theme }) => ({
  position: "absolute",
  top: 0,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  borderRight: `1px solid ${theme.palette.divider}`,
  textAlign: "center",
  //   backgroundColor: theme.palette.grey[50],
  padding: theme.spacing(1),
  height: "50px",
}));

export const CategoryRow = styled(Box)(({ theme }) => ({
  backgroundColor: "rgb(37 53 69 / 52%)", //theme.palette.grey[100],
}));

export const StickyHeaderCell = styled(Box)(({ theme }) => ({
  position: "sticky",
  left: 0,
  top: 0,
  zIndex: 20,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  borderRight: `2px solid ${theme.palette.divider}`,
  backgroundColor: "orange", //theme.palette.grey[50],
  padding: theme.spacing(1),
  height: "50px",
}));

export const StickyDataCell = styled(Box)(({ theme }) => ({
  position: "sticky",
  left: 0,
  zIndex: 15,
  borderRight: `2px solid ${theme.palette.divider}`,
  borderBottom: `1px solid ${theme.palette.divider}`,
  display: "flex",
  alignItems: "center",
  padding: theme.spacing(1),
  height: "100%",
}));
