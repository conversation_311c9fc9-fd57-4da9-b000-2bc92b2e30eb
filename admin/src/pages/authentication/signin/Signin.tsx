import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Checkbox from "@mui/material/Checkbox";
import FormControlLabel from "@mui/material/FormControlLabel";
import Divider from "@mui/material/Divider";
// import Link from "@mui/material/Link";
import Typography from "@mui/material/Typography";
import { useAuth } from "src/hooks/useAuth";
import { FormProvider, useForm } from "react-hook-form";
import { SiginInputsType } from "./types";
import { yupResolver } from "@hookform/resolvers/yup";
import { SCHEMA } from "./schema";
import { Input } from "src/components/form";
import { GoogleIcon } from "../styled-components/GoogleIcon";
// import ForgotPassword from "./components/ForgotPassword";

export default function Signin() {
  const { login, googleLogin } = useAuth();

  const form = useForm<SiginInputsType>({
    defaultValues: {
      email: "",
      password: "",
    },
    resolver: yupResolver(SCHEMA),
  });
  const {
    handleSubmit,
    formState: { errors },
  } = form;
  // const [open, setOpen] = useState(false);

  // const handleClickOpen = () => {
  //   setOpen(true);
  // };

  // const handleClose = () => {
  //   setOpen(false);
  // };

  const onSubmit = (data: SiginInputsType) => {
    login(data);
  };

  return (
    <>
      <Typography
        component="h1"
        variant="h4"
        sx={{ width: "100%", fontSize: "clamp(2rem, 10vw, 2.15rem)" }}
      >
        Sign in
      </Typography>
      <FormProvider {...form}>
        <Box
          component="form"
          onSubmit={handleSubmit(onSubmit)}
          noValidate
          sx={{
            display: "flex",
            flexDirection: "column",
            width: "100%",
            gap: 2,
          }}
        >
          <Input
            name="email"
            label={"Email"}
            autoComplete="email"
            error={!!errors.email}
            helperText={errors.email?.message}
            id="email"
            type="email"
            placeholder="<EMAIL>"
            autoFocus
            required
            fullWidth
            variant="outlined"
            color={errors.email ? "error" : "primary"}
          />

          <Input
            name="password"
            label={"Password"}
            error={!!errors.password}
            helperText={errors.password?.message}
            placeholder="••••••"
            type="password"
            id="password"
            autoComplete="current-password"
            required
            fullWidth
            variant="outlined"
            color={errors.password ? "error" : "primary"}
          />

          <FormControlLabel
            control={<Checkbox value="remember" color="primary" />}
            label="Remember me"
          />
          {/* <ForgotPassword open={open} handleClose={handleClose} /> */}
          <Button type="submit" fullWidth variant="contained">
            Sign in
          </Button>
          {/* <Link
            component="button"
            type="button"
            onClick={handleClickOpen}
            variant="body2"
            sx={{ alignSelf: "center" }}
          >
            Forgot your password?
          </Link> */}
        </Box>
      </FormProvider>
      <Divider>or</Divider>
      <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
        <Button
          fullWidth
          variant="outlined"
          onClick={googleLogin}
          startIcon={<GoogleIcon />}
        >
          Sign in with Google
        </Button>
      </Box>
    </>
  );
}
