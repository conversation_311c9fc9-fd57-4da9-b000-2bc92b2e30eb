// import i18next from 'i18next';
import * as Yup from "yup";

// const requiredField = Yup.string().when(
//   "$isFirstStep",
//   (isFirstStep, schema) => (!isFirstStep ? schema.required() : undefined)
// );

export const SCHEMA = Yup.object().shape({
  name: Yup.string().required().min(2).max(30),
  email: Yup.string().email().required(),
  password: Yup.string().required(),
  // .matches(
  //   /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])[0-9a-zA-Z!@#$&()\\-`.+,/"]{8,}$/,
  //   "Password must contain at least 8 characters, one uppercase, one number and one special case character"
  // ),
  validatePassword: Yup.string()
    .required()
    .oneOf([Yup.ref("password"), null], "Passwords must match"),
  // country: Yup.string().required(),
  company: Yup.string().required(),
  phone: Yup.string().required(),
});
