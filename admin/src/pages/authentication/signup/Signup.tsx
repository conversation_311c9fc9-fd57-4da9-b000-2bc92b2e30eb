import { yup<PERSON><PERSON>olver } from "@hookform/resolvers/yup";
import { <PERSON><PERSON><PERSON>ider, SubmitHandler, useForm } from "react-hook-form";
import { Input, PasswordInput } from "src/components/form";
// import CountrySelect from "src/components/form/country-select";

import { SCHEMA } from "./schema";
import { SignupInputsType } from "./types";
import { Box, Button, Divider, Typography } from "@mui/material";
import { useAuth } from "src/hooks/useAuth";
import { GoogleIcon } from "../styled-components/GoogleIcon";

export function Signup() {
  // const { t } = useLocales();
  const { signup, googleLogin } = useAuth();
  const form = useForm<SignupInputsType>({
    defaultValues: {
      company: "",
      validatePassword: "",
      // country: "", TODO: Uncomment this line
      email: "",
      name: "",
      password: "",
      phone: "",
    },
    resolver: yup<PERSON><PERSON><PERSON><PERSON>(SCHEMA),
  });

  const { handleSubmit } = form;

  const onSubmit: SubmitHandler<SignupInputsType> = async (data) => {
    await signup(data);
  };

  return (
    <>
      <Typography
        component="h1"
        variant="h4"
        sx={{ width: "100%", fontSize: "clamp(2rem, 10vw, 2.15rem)" }}
      >
        Sign up
      </Typography>
      <FormProvider {...form}>
        <Box
          component="form"
          onSubmit={handleSubmit(onSubmit)}
          sx={{ display: "flex", flexDirection: "column", gap: 2 }}
        >
          <Input
            id="name"
            name="name"
            label="Full name"
            autoComplete="name"
            required
            fullWidth
            placeholder="Jon Snow"
            // error={nameError}
            // helperText={nameErrorMessage}
            // color={nameError ? "error" : "primary"}
          />
          <Input
            id="email"
            name="email"
            label={"Email"}
            required
            fullWidth
            placeholder="<EMAIL>"
            autoComplete="email"
          />
          <PasswordInput
            name="password"
            label={"Password"}
            helperText={"Password must be at least 8 characters long."}
            autoComplete="new-password"
          />
          <PasswordInput
            name="validatePassword"
            label={"Confirm password"}
            autoComplete="new-password"
          />
          <Input
            id="company"
            name="company"
            label={"Company"}
            // required
            fullWidth
            placeholder="Company name"
            autoComplete="company"
          />
          <Input name="phone" label={"Phone number"} type="tel" />
          {/* <CountrySelect name="country" optionKey="iso" /> */}

          <Button
            fullWidth
            size="large"
            type="submit"
            variant="contained"
            // loading={loading}
          >
            Sign up
          </Button>
          <Divider>
            <Typography sx={{ color: "text.secondary" }}>or</Typography>
          </Divider>
          <Button
            fullWidth
            variant="outlined"
            onClick={googleLogin}
            startIcon={<GoogleIcon />}
          >
            Sign up with Google
          </Button>
        </Box>
      </FormProvider>
    </>
  );
}
