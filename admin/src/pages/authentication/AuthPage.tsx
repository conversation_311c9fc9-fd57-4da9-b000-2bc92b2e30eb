import { Tab, Tabs } from "@mui/material";
import { SyntheticEvent, useState } from "react";
import { TabPanel } from "src/components/tab-panel/TabPanel";
import Signup from "./signup";
import Signin from "./signin/Signin";
import { FormContainer } from "./styled-components/FormContainer";
import { FormCard } from "./styled-components/FormCard";

export default function AuthPage() {
  const [value, setValue] = useState(0);

  const handleChange = (_: SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <FormContainer direction="column" justifyContent="center">
      <FormCard variant="outlined">
        <Tabs
          value={value}
          onChange={handleChange}
          aria-label="basic tabs example"
        >
          <Tab
            label="Login"
            id="simple-tab-0"
            aria-controls="simple-tabpanel-0"
          />
          <Tab
            label="Sign up"
            id="simple-tab-1"
            aria-controls="simple-tabpanel-1"
          />
        </Tabs>
        <TabPanel value={value} index={0}>
          <Signin />
        </TabPanel>
        <TabPanel value={value} index={1}>
          <Signup />
        </TabPanel>
      </FormCard>
    </FormContainer>
  );
}
