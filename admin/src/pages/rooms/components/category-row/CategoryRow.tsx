import { MouseEvent, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Switch,
  IconButton,
  Typography,
  Box,
  Menu,
  MenuItem,
  Collapse,
} from "@mui/material";
import {
  MoreHoriz as MoreHorizIcon,
  KeyboardArrowDown as KeyboardArrowDownIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon,
} from "@mui/icons-material";
import { RoomCategory } from "../../types";

export const CategoryRow = ({
  category,
  onToggle,
}: {
  category: RoomCategory;
  onToggle: (id: number) => void;
}) => {
  const [open, setOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleMenuClick = (event: MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  return (
    <>
      <TableRow sx={{ "& > *": { borderBottom: "unset" } }} hover>
        <TableCell>
          <IconButton
            aria-label="expand row"
            size="small"
            onClick={() => setOpen(!open)}
          >
            {open ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
          </IconButton>
        </TableCell>
        <TableCell>
          <Typography
            component="a"
            href="#"
            sx={{
              color: "#1976d2",
              textDecoration: "underline",
              "&:hover": {
                textDecoration: "none",
              },
            }}
          >
            {category.name}
          </Typography>
        </TableCell>
        <TableCell>{category.shortName}</TableCell>
        <TableCell sx={{ textAlign: "center" }}>{5}</TableCell>
        <TableCell sx={{ textAlign: "center" }}>
          {category.mainPlaces}
        </TableCell>
        <TableCell sx={{ textAlign: "center" }}>
          {category.additionalPlaces}
        </TableCell>
        <TableCell sx={{ textAlign: "center" }}>
          <Switch
            checked={category.enabled}
            onChange={() => onToggle(category.id)}
            color="success"
            size="small"
          />
        </TableCell>
        <TableCell sx={{ textAlign: "center" }}>
          <IconButton
            size="small"
            onClick={handleMenuClick}
            sx={{ color: "#9e9e9e" }}
          >
            <MoreHorizIcon />
          </IconButton>
        </TableCell>
      </TableRow>
      <TableRow>
        <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={8}>
          <Collapse in={open} timeout="auto" unmountOnExit>
            <Box sx={{ margin: 1 }}>
              <Typography variant="h6" gutterBottom component="div">
                Rooms in {category.name}
              </Typography>

              {!category.rooms.length ? (
                <Typography
                  variant="h6"
                  component="div"
                  sx={{ textAlign: "center" }}
                >
                  No rooms found
                </Typography>
              ) : (
                <Table size="small" aria-label="rooms">
                  <TableHead>
                    <TableRow>
                      <TableCell>Room Number</TableCell>
                      <TableCell>Building</TableCell>
                      <TableCell>Floor</TableCell>
                      <TableCell>Category</TableCell>
                      <TableCell>Comments</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {category.rooms.map((room) => (
                      <TableRow key={room.id}>
                        <TableCell component="th" scope="row">
                          <Typography
                            component="a"
                            href="#"
                            sx={{
                              color: "#1976d2",
                              textDecoration: "underline",
                              "&:hover": {
                                textDecoration: "none",
                              },
                            }}
                          >
                            {room.roomNumber}
                          </Typography>
                        </TableCell>
                        <TableCell>{room.building}</TableCell>
                        <TableCell>{room.floor}</TableCell>
                        <TableCell>{room.category}</TableCell>
                        <TableCell>{room.comments}</TableCell>
                        <TableCell align="right">
                          <IconButton size="small" sx={{ color: "#9e9e9e" }}>
                            <MoreHorizIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </Box>
          </Collapse>
        </TableCell>
      </TableRow>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
      >
        <MenuItem onClick={handleMenuClose}>Edit</MenuItem>
        <MenuItem onClick={handleMenuClose}>Duplicate</MenuItem>
        <MenuItem onClick={handleMenuClose}>Delete</MenuItem>
      </Menu>
    </>
  );
};
