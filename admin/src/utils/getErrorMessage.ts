type TErrorWithMessage = {
  message: string;
};

function isErrorWithMessage(error: unknown): error is TErrorWithMessage {
  return (
    typeof error === "object" &&
    error !== null &&
    "message" in error &&
    typeof (error as Record<string, unknown>).message === "string"
  );
}

function toErrorWithMessage(maybeError: unknown): TErrorWithMessage {
  if (isErrorWithMessage(maybeError)) return maybeError;

  try {
    return new Error(JSON.stringify(maybeError));
  } catch {
    // fallback in case there's an error stringifying the maybeError
    return new Error(String(maybeError));
  }
}

export function getErrorMessage(error: {
  data: Record<string, unknown>;
}): string {
  if (error.data && typeof error.data === "object") {
    return Object.values(error.data)
      .flatMap((m) => m)
      .join(" • ");
  }

  return toErrorWithMessage(error).message;
}
