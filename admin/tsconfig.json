{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "jsxImportSource": "@emotion/react", "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "sourceMap": true, "baseUrl": ".", "paths": {"src/*": ["src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.spec.ts"]}