import axios from "axios";
import { Request, Response, Router } from "express";
import { requestHelper } from "src/helpers";
import { logger } from "src/log";

import {
  Code,
  CoreApiResponse,
  EnumQueue,
  handleError,
  // mimeByGroup,
  REFRESH_TOKEN,
  // REQUEST_TYPE,
  RequestType,
  validate,
} from "src/core/common";
import {
  getRefreshTokenCookieSettings,
  rateLimiter,
} from "src/core/common/util";
// import EmailApiClient from "src/core/service/postmark";
import { ForgotPasswordRequest, ResetPasswordRequest } from "src/dto";

import reCaptcha from "src/middlewares/reCaptcha";
import { EmailQueue } from "src/queues";

import AuthController from "src/controllers/auth";
import initUserMiddleware from "src/middlewares/initUserMiddleware";
import authMiddleware from "src/middlewares/authMiddleware";

class AuthRouter {
  router: Router;

  emailQueue: EmailQueue;

  authController: AuthController;

  constructor() {
    this.router = Router();

    this.emailQueue = new EmailQueue();

    this.authController = new AuthController({
      [EnumQueue.Email]: this.emailQueue,
    });
    this.init();
  }

  // @handleError("auth/login")
  // public async login(request: RequestType<Request>, res: Response) {
  //   const deviceInfo = {
  //     ...request.body.deviceInfo,
  //     fingerprint: request.get("X-Device-Fingerprint"),
  //   };

  //   let response: any = await this.authController.userLogin({
  //     email: request.body.email,
  //     password: request.body.password,
  //     googleId: request.body.googleId,
  //     origin: request.get("origin"),
  //     deviceInfo: deviceInfo,
  //   });
  //   if (response.user?.twoFA) {
  //     response = { twoFA: true };
  //   }

  //   const { refreshToken, ...restOfResponse } = response;
  //   res.cookie(REFRESH_TOKEN, refreshToken, getRefreshTokenCookieSettings());

  //   return res.json(CoreApiResponse.success(restOfResponse));
  // }

  @handleError("auth/google")
  public async googleLogin(request: RequestType<Request>, res: Response) {
    // const deviceInfo = {
    //   ...request.body.deviceInfo,
    //   fingerprint: request.get("X-Device-Fingerprint"),
    // };

    let response: any = await this.authController.googleLogin({
      code: request.body.code,
      origin: request.get("origin"),
      // deviceInfo: deviceInfo,
    });

    if (response.user?.twoFA) {
      response = { twoFA: true };
    }

    const { refreshToken, ...restOfResponse } = response;
    res.cookie(REFRESH_TOKEN, refreshToken, getRefreshTokenCookieSettings());

    return res.json(CoreApiResponse.success(restOfResponse));
  }

  @validate(ForgotPasswordRequest)
  @handleError("auth/forgotPassword")
  public async forgotPassword(request: Request, res: Response) {
    await this.authController.forgotPassword(request.body.email);
    return res.json({ message: "Success" });
  }

  @validate(ResetPasswordRequest)
  @handleError("auth/resetPassword")
  public async resetPassword(request: Request, res: Response) {
    const { passwordToken, newPassword } = request.body;
    const response = await this.authController.resetPassword(
      passwordToken,
      newPassword
    );
    return res.json({ message: "Success", ...response });
  }

  @handleError("auth/confirmEmail")
  public async confirmEmail(request: Request, res: Response) {
    const deviceInfo = {
      ...request.body.deviceInfo,
      fingerprint: request.get("X-Device-Fingerprint"),
    };
    const response = await this.authController.confirmEmail(
      request.body.emailToken,
      deviceInfo
    );

    const { refreshToken, ...restOfResponse } = response;
    res.cookie(REFRESH_TOKEN, refreshToken, getRefreshTokenCookieSettings());

    return res.json({ message: "Success", ...restOfResponse });
  }

  public utcTimeConvertor(timezone: number, start: any, end: any) {
    if (!timezone) timezone = 0;
    start = start.utcOffset(timezone);
    end = end.utcOffset(timezone);

    return {
      start: start.format("HH:mm"),
      end: end.format("HH:mm"),
      date: start.format("DD/MM/YYYY"),
    };
  }

  @handleError("auth/downloadPdf")
  public async downloadPdf(request: RequestType<Request>, res: Response) {
    const url = request.body.url;

    axios({
      url,
      method: "GET",
      responseType: "stream",
      headers: {
        "Content-Type": "application/pdf",
      },
    })
      .then((response) => {
        // Ensure that the client receives the PDF file as a downloadable attachment
        res.setHeader("Content-Disposition", `attachment; filename=file.pdf`);
        res.setHeader("Content-Type", "application/pdf");

        response.data.pipe(res);
      })
      .catch((error) => {
        logger.error(error);
        return res.status(500).json({
          message: "Failed to download the pdf, please try again",
        });
      });
  }

  @handleError("auth/getCurrencies/getAll")
  public async getCurrencies(request: RequestType<Request>, res: Response) {
    const { isPayment } = request.body;
    const currencies = await new DbCurrency().getAll({
      query: isPayment ? { isPayment } : {},
    });
    return res.status(200).json({ message: "Success", currencies });
  }

  public async createQRCode(request: RequestType<Request>, res: Response) {
    // req.body object should look like this
    //   {
    //     "r":{
    //          "id": 100,
    //          "t":"Tour Name", // title
    //          "s" : "7ed6994e", // slug
    //      }
    //  }
    const jsonData = JSON.stringify(request.body);
    const { data: qrLink } = await requestHelper.fetch(
      `https://api.qrserver.com/v1/create-qr-code/?data=${jsonData}&size=150x150`,
      { method: "GET" }
    );

    if (!qrLink)
      return res.json(
        CoreApiResponse.error(
          Code.BAD_REQUEST_ERROR.code,
          "Could not generate QR link"
        )
      );
    return res.json(CoreApiResponse.success(qrLink));
  }

  /**
   * Take each handler, and attach to one of the Express.Router's
   * endpoints.
   */
  init() {
    this.router.get(
      "/me",
      rateLimiter.middleware({
        blockDuration: 30,
        points: 10,
      }),
      initUserMiddleware,
      this.authController.getUser.bind(this)
    );
    this.router.post(
      "/signup",
      reCaptcha,
      rateLimiter.middleware({
        blockDuration: 30,
        points: 10,
      }),
      this.authController.signup.bind(this)
    );
    this.router.post(
      "/login",
      reCaptcha,
      rateLimiter.middleware({
        blockDuration: 15,
        points: 10,
        duration: 60,
      }),
      this.authController.userLogin.bind(this)
    );

    this.router.post(
      "/google",
      reCaptcha,
      rateLimiter.middleware({
        blockDuration: 15,
        points: 10,
        duration: 60,
      }),
      this.googleLogin.bind(this)
    );

    this.router.post(
      "/forgotPassword",
      reCaptcha,
      rateLimiter.middleware({
        blockDuration: 15,
        points: 10,
        duration: 60,
      }),
      this.forgotPassword.bind(this)
    );
    this.router.post(
      "/resetPassword",
      reCaptcha,
      this.resetPassword.bind(this)
    );
    this.router.post(
      "/confirmEmail",
      rateLimiter.middleware({
        blockDuration: 15,
        points: 10,
        duration: 60,
      }),
      this.confirmEmail.bind(this)
    );

    this.router.post("/downloadPdf", this.downloadPdf.bind(this));

    this.router.get("/getCurrencies", this.getCurrencies.bind(this));

    this.router.post("/createQRCode", this.createQRCode.bind(this));

    this.router.post(
      "/logout",
      rateLimiter.middleware({
        blockDuration: 30,
        points: 10,
      }),
      authMiddleware,
      this.authController.logOutUser.bind(this)
    );
  }
}

export default AuthRouter;
