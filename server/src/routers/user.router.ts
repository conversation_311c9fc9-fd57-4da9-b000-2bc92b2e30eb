import * as path from "node:path";

import { format } from "@fast-csv/format";

import { Request, Response, Router } from "express";
import { pick } from "lodash";
import config from "src/config";
import StripePayment from "src/core/service/stripe";
import { requestHelper } from "src/helpers";
import { logger } from "src/log";
import { uuid } from "uuidv4";

import UserController from "../controllers/user";
import {
  ClientException,
  Code,
  CoreApiResponse,
  handleError,
  mimeByGroup,
  rateLimiter,
  REQUEST_TYPE,
  RequestType,
  upload,
  validate,
} from "../core/common";
// import {
//   GetUserAdminResponse,
//   IdParam as IdParameter,
//   UpdatePermissionRequest,
//   UserLoginSettingsRequest,
//   UserLoginSettingsResponse,
// } from '../dto';
// import authorize from '../lib/permission';
import { BotQueue, EmailQueue, EnumQueue, S3Queue } from "../queues";
// import {
//   addMailchimp,
//   alreadyRegistered,
//   cancelPaypalSubscription,
//   createPaypalPayout,
//   getCurrency,
//   getLoginBackgroundURL,
//   getLoginLogoURL,
//   getPaypalToken,
//   subscribeMailchimp,
//   unsubscribeMailchimp,
// } from './util';

const crypto = require("node:crypto");
const passwordHash = require("password-hash");
const moment = require("moment");

const xlsx = require("xlsx");
const uploadPath = require("node:path").resolve(__dirname, "../../");

class UserRouter {
  router: Router;

  botQueue: BotQueue;

  s3Queue: S3Queue;

  emailQueue: EmailQueue;

  // awsQueue: AWSQueue;

  controller: UserController;

  stripe: StripePayment;

  constructor() {
    this.botQueue = new BotQueue();
    this.s3Queue = new S3Queue();
    this.emailQueue = new EmailQueue();
    // this.awsQueue = new AWSQueue();
    this.stripe = new StripePayment({ apiKey: config.get("stripe.apiKey") });
    this.router = Router();
    this.init();

    this.controller = new UserController({
      [EnumQueue.Email]: this.emailQueue,
      [EnumQueue.Storage]: this.s3Queue,
      // [EnumQueue.Aws]: this.awsQueue,
    });
  }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError("user/subscription/add")
  // public async addSubscription(request: RequestType<Request>, res: Response) {
  //   const user = await new DbUser().fromId(request.account.id);
  //   if (user.subscriptionId)
  //     return res
  //       .status(422)
  //       .json({ message: "Already have active subscription" });
  //   if (user.manualSubscription) user.manualSubscription = false;
  //   user.subscriptionId = request.body.subscriptionId;
  //   await user.save();
  //   logger.info(`user/subscription/add: id ${user.id}`);

  //   return res.status(200).json({ message: "Success" });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError("user/whitelabel/pay")
  // public async payWhitelabel(request: RequestType<Request>, res: Response) {
  //   const userId = request.body.userId
  //     ? request.body.userId
  //     : request.account.id;
  //   const user = await new DbUser().fromId(userId);
  //   user.whitelabelId = request.body.subscriptionId;
  //   await user.save();

  //   if (!request.body.userId) {
  //     // In this case the user pays for himself and needs to have an email
  //     this.emailQueue
  //       .sendEmail("newWhitelabel", process.env.GRIDEVO_EMAIL, user.email, {
  //         name: user.name,
  //       })
  //       .catch((error: any) => {
  //         logger.error(`user/whitelabel/pay: ${error.message}`);
  //       });
  //   }

  //   logger.info(`user/whitelabel/pay: id ${user.id}`);

  //   return res.status(200).json({ message: "Success" });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError("user/clientWhitelabel/pay")
  // public async payClientWhitelabel(
  //   request: RequestType<Request>,
  //   res: Response
  // ) {
  //   const { type, id } = request.account;
  //   const client = await new DbClient().fromPk(request.body.clientId);

  //   if (type !== Role.SuperAdmin && client.userId !== id) {
  //     throw ClientException.new({
  //       code: Code.ACCESS_DENIED_ERROR,
  //     });
  //   }

  //   if (client.whitelabelId) {
  //     throw ClientException.new({
  //       code: Code.VALIDATION_ERROR,
  //       overrideMessage: "Already have active subscription",
  //     });
  //   }

  //   client.whitelabelId = request.body.subscriptionId;
  //   client.whitelabelLogo = request.body.logo;
  //   client.primaryColor = "#292929";
  //   client.secondaryColor = "#ea475c";
  //   await client.save(client.userId);

  //   logger.info(`user/clientWhitelabel/pay: id ${client.id}`);

  //   return res.status(200).json({ message: "Success" });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError("user/credits/buy")
  // public async buyCredits(request: RequestType<Request>, res: Response) {
  //   const user = await new DbUser().fromId(request.account.id);

  //   if (user.id !== 1 && user.adminId !== 1271 && !request.body.orderId) {
  //     return res
  //       .status(500)
  //       .json({ message: "Not allowed to get credits without a purchase" });
  //   }

  //   if (request.body.orderId) {
  //     const newPayment = await new DbPayment();
  //     newPayment.orderId = request.body.orderId;
  //     await newPayment.save(request.account.id);
  //   }

  //   const credits = Number.parseInt(request.body.credits, 10);
  //   if (credits) {
  //     user.videoCredits += credits;
  //     user.purchasedCredits += credits;
  //     user.lastCreditsPurchase = new Date();
  //   }

  //   const minutesToBuy = Number.parseInt(request.body.minutesToBuy, 10);
  //   if (minutesToBuy) {
  //     user.hubMinutes += minutesToBuy;
  //   }

  //   await user.save();
  //   logger.info(`user/credits/buy: id ${user.id}`);

  //   return res
  //     .status(200)
  //     .json({ message: "Success", credits: user.videoCredits });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError("user/credits/transfer")
  // public async transferHubMinutes(
  //   request: RequestType<Request>,
  //   res: Response
  // ) {
  //   const credits = await this.controller.transferHubMinutes({
  //     account: request.account,
  //     clientId: request.body.clientId,
  //     type: request.body.type,
  //     hubMinutes: request.body.hubMinutes,
  //   });
  //   return res.status(200).json({ message: "Success", credits });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError("user/updateHubspot")
  // public async updateHubspot(request: RequestType<Request>, res: Response) {
  //   const response = await this.controller.updateHubspot({
  //     email: request.body.email,
  //     properties: request.body.properties,
  //   });
  //   return res.status(200).json({ message: "Success", response });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError("user/createZendeskTicket")
  // public async createZendeskTicket(
  //   request: RequestType<Request>,
  //   res: Response
  // ) {
  //   const { body, files } = request;
  //   const response = await this.controller.createZendeskTicket(
  //     body,
  //     files.files
  //   );
  //   return res.status(200).json({ message: "Success", response });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError("user/subscription/cancel")
  // public async cancelSubscription(
  //   request: RequestType<Request>,
  //   res: Response
  // ) {
  //   const user = await new DbUser().fromId(request.account.id);
  //   if (!user.subscriptionId && !user.manualSubscription)
  //     return res.status(422).json({ message: "No active subscription yet" });
  //   if (!user.manualSubscription) {
  //     cancelPaypalSubscription(user.subscriptionId).catch((error) => {
  //       logger.error(error.message);
  //     });
  //   }

  //   user.manualSubscription = false;
  //   user.subscription = UserSubscription.Pay;
  //   await user.save();

  //   logger.info(`user/subscription/cancel: id ${user.id}`);
  //   return res.status(200).json({ message: "Success" });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin])
  // @handleError("user/subscription/edit")
  // public async editSubscription(request: RequestType<Request>, res: Response) {
  //   const user = await new DbUser().fromId(request.body.id);
  //   const newSubscription = request.body.newSubscription;
  //   const subscriptionId = request.body.subscriptionId;

  //   if (user.subscription == newSubscription) {
  //     logger.info(
  //       `user/subscription/edit: subscription doesn't need to be updated, user id: ${user.id}`
  //     );
  //     return;
  //   }

  //   if (!user.manualSubscription) {
  //     if (user.subscriptionId)
  //       await cancelPaypalSubscription(user.subscriptionId);
  //     user.manualSubscription = true;
  //     await user.save();
  //   }

  //   if (subscriptionId && subscriptionId != user.subscriptionId) {
  //     user.subscriptionId = subscriptionId;
  //     user.manualSubscription = true;
  //   }

  //   if (
  //     user.subscription != UserSubscription.Pay &&
  //     newSubscription == UserSubscription.Pay
  //   ) {
  //     const tours = await new DbTour().getAllNoLimit(user.id);
  //     await Promise.all(
  //       tours.map(async (tour: any) => {
  //         if (tour.status == TourStatus.Error || tour.status == TourStatus.Idle)
  //           return;

  //         if (
  //           tour.subscriptionId?.includes("I-") &&
  //           tour.status === TourStatus.Ready
  //         )
  //           cancelPaypalSubscription(tour.subscriptionId).catch((error) => {
  //             logger.error(error.message);
  //           });

  //         tour.status = TourStatus.Pay;
  //         tour.subscriptionId = null;
  //         await tour.save();

  //         return;
  //       })
  //     );

  //     user.subscription = newSubscription;
  //     user.subscriptionStart = new Date();
  //     await user.save();
  //   }

  //   if (
  //     user.subscription == UserSubscription.Pay &&
  //     newSubscription != UserSubscription.Pay
  //   ) {
  //     const tours = await new DbTour().getAllNoLimit(user.id);
  //     await Promise.all(
  //       tours.map(async (tour: any) => {
  //         if (tour.status == TourStatus.Error || tour.status == TourStatus.Idle)
  //           return;

  //         if (
  //           tour.subscriptionId?.includes("I-") &&
  //           tour.status === TourStatus.Ready
  //         )
  //           cancelPaypalSubscription(tour.subscriptionId).catch((error) => {
  //             logger.error(error.message);
  //           });

  //         if (!tour.scannedAt) {
  //           tour.status = TourStatus.Idle;
  //           await tour.save();
  //           this.botQueue
  //             .scanTour({
  //               id: tour.id,
  //               io: request.io,
  //             })
  //             .catch((error) => {
  //               logger.error(
  //                 `Error scanning tour ${tour.id}: ${error.message}`
  //               );
  //             });
  //         } else {
  //           tour.status = TourStatus.Ready;
  //           request.io.emit("bot-job-done", tour);
  //           await tour.save();
  //         }

  //         return;
  //       })
  //     );

  //     user.subscription = newSubscription;
  //     user.subscriptionStart = new Date();
  //     await user.save();
  //   }

  //   if (
  //     user.subscription != UserSubscription.Pay &&
  //     newSubscription != UserSubscription.Pay
  //   ) {
  //     user.subscription = newSubscription;
  //     user.subscriptionStart = new Date();
  //     await user.save();
  //   }

  //   logger.info(`user/subscription/edit: id ${user.id}`);
  //   return res.status(200).json({ message: "Success" });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin])
  // @handleError("user/subscription/extendTrial")
  // public async extendTrialSubscription(
  //   request: RequestType<Request>,
  //   res: Response
  // ) {
  //   const user = await new DbUser().fromId(request.body.id);
  //   user.subscriptionStart = new Date();
  //   await user.save();

  //   logger.info(`user/subscription/extendTrial: id ${user.id}`);
  //   return res.status(200).json({ message: "Success" });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError("user/subscription/stopTrial")
  // public async stopTrialSubscription(
  //   request: RequestType<Request>,
  //   res: Response
  // ) {
  //   const user = await new DbUser().fromId(request.account.id);
  //   if (user.subscription !== UserSubscription.Trial) {
  //     throw ClientException.new({
  //       code: Code.BAD_REQUEST_ERROR,
  //       overrideMessage: "User not on trial",
  //     });
  //   }
  //   user.subscriptionStart = new Date();
  //   user.subscription = UserSubscription.Free;
  //   await user.save();

  //   const tours = await new DbTour().getAllNoLimit(user.id);
  //   await Promise.all(
  //     tours.map(async (tour: any) => {
  //       if (tour.status == TourStatus.Error || tour.status == TourStatus.Idle)
  //         return;

  //       tour.status = TourStatus.Disabled;
  //       await tour.save();

  //       return;
  //     })
  //   );

  //   logger.info(`user/subscription/stopTrial: id ${user.id}`);
  //   return res.status(200).json({ message: "Success" });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError("user/whitelabel/cancel")
  // public async cancelWhitelabel(request: RequestType<Request>, res: Response) {
  //   const id =
  //     request.account.type === Role.SuperAdmin
  //       ? request.body.id
  //       : request.account.id;
  //   const user = await new DbUser().fromId(id || request.account.id);
  //   if (!user.whitelabelId)
  //     return res.status(422).json({ message: "No active whitelabel yet" });

  //   if (user.whitelabelId.includes("I-")) {
  //     cancelPaypalSubscription(user.whitelabelId).catch((error) => {
  //       logger.error(error.message);
  //     });
  //   }

  //   if (user.cloudfrontId && user.domainUrl)
  //     this.controller
  //       .removeDNSRecords(user.id, request.account)
  //       .catch((error) => {
  //         logger.error(error.message);
  //       });

  //   user.whitelabelId = null;
  //   user.domainUrl = null;
  //   user.whitelabelEmail = null;
  //   user.primaryColor = null;
  //   user.secondaryColor = null;
  //   user.cloudfrontId = null;
  //   await user.save();

  //   logger.info(`user/whitelabel/cancel: id ${user.id}`);
  //   return res.status(200).json({ message: "Success" });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError("user/clientWhitelabel/cancel")
  // public async cancelClientWhitelabel(
  //   request: RequestType<Request>,
  //   res: Response
  // ) {
  //   const { id, type } = request.account;
  //   const client = await new DbClient().fromPk(request.body.clientId);
  //   if (type !== Role.SuperAdmin && client.userId !== id) {
  //     throw ClientException.new({
  //       code: Code.ACCESS_DENIED_ERROR,
  //     });
  //   }

  //   if (!client.whitelabelId)
  //     return res.status(422).json({ message: "No active whitelabel yet" });

  //   if (client.whitelabelId.includes("I-")) {
  //     cancelPaypalSubscription(client.whitelabelId).catch((error) => {
  //       logger.error(error.message);
  //     });
  //   }

  //   client.whitelabelId = null;
  //   client.primaryColor = null;
  //   client.secondaryColor = null;
  //   client.whitelabelLogo = false;
  //   await client.save(client.userId);

  //   logger.info(`user/clientWhitelabel/cancel: id ${client.id}`);
  //   return res.status(200).json({ message: "Success" });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError("user/subscription/upgrade")
  // public async upgradeSubscription(
  //   request: RequestType<Request>,
  //   res: Response
  // ) {
  //   const user = await new DbUser().fromId(request.account.id);
  //   if (!user.subscriptionId && !user.manualSubscription)
  //     return res.status(422).json({ message: "No active subscription yet" });

  //   if (user.manualSubscription) {
  //     user.manualSubscription = false;
  //     user.subscription = UserSubscription.Pay;
  //     await user.save();
  //     return res.status(422).json({ message: "Subscription has been reset!" });
  //   }

  //   //From month to year
  //   const token = await getPaypalToken();
  //   const currency = getCurrency(user.country).ISO;
  //   return requestHelper
  //     .fetch(
  //       `${process.env.PAYPAL_API}/v1/billing/subscriptions/${user.subscriptionId}/revise`,
  //       {
  //         method: "POST",
  //         headers: {
  //           Authorization: `Bearer ${token}`,
  //         },
  //         data: {
  //           plan_id: process.env[`PAYPAL_YEARLY_PLAN_${currency}`],
  //         },
  //       }
  //     )
  //     .then((ans) => {
  //       const link = ans.data.links.find((l: any) => {
  //         return l.rel == "approve";
  //       });
  //       logger.info(`user/subscription/upgrade: id ${user.id}`);

  //       return res.status(200).json({ message: "Success", link: link.href });
  //     })
  //     .catch((error: any) => {
  //       return res.status(500).json({ message: error.message });
  //     });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError("user/subscription/downgrade")
  // public async downgradeSubscription(
  //   request: RequestType<Request>,
  //   res: Response
  // ) {
  //   const user = await new DbUser().fromId(request.account.id);
  //   if (!user.subscriptionId && !user.manualSubscription)
  //     return res.status(422).json({ message: "No active subscription yet" });

  //   if (user.manualSubscription) {
  //     user.manualSubscription = false;
  //     user.subscription = UserSubscription.Pay;
  //     await user.save();
  //     return res.status(422).json({ message: "Subscription has been reset!" });
  //   }

  //   //From year to month
  //   const token = await getPaypalToken();
  //   const currency = getCurrency(user.country).ISO;
  //   return requestHelper
  //     .fetch(
  //       `${process.env.PAYPAL_API}/v1/billing/subscriptions/${user.subscriptionId}/revise`,
  //       {
  //         method: "POST",
  //         headers: {
  //           Authorization: `Bearer ${token}`,
  //         },
  //         data: {
  //           plan_id: process.env[`PAYPAL_MONTHLY_PLAN_${currency}`],
  //         },
  //       }
  //     )
  //     .then((ans) => {
  //       const link = ans.data.links.find((l: any) => {
  //         return l.rel == "approve";
  //       });
  //       logger.info(`user/subscription/downgrade: id ${user.id}`);

  //       return res.status(200).json({ message: "Success", link: link.href });
  //     })
  //     .catch((error: any) => {
  //       logger.error(`user/subscription/downgrade:${error.message}`);
  //       return res.status(500).json({ message: error.message });
  //     });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin])
  // @handleError("user/add")
  // public async add(request: RequestType<Request>, res: Response) {
  //   const user = await this.controller.add(request.body, request.account.id);
  //   return res.status(200).json({ message: "Success", user });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError("user/edit")
  // public async edit(request: RequestType<Request>, res: Response) {
  //   const user = await this.controller.edit({
  //     account: request.account,
  //     data: request.body,
  //     file: request.file,
  //   });

  //   if (process.env.NODE_ENV != "local") {
  //     let domain = "";

  //     if (user?.domainUrl) {
  //       domain = user.domainUrl;
  //     }

  //     if (process.env.NODE_ENV == "production") {
  //       domain = user?.isNewAdmin
  //         ? user.domainUrl || "my.gridevo.com"
  //         : user.adminUrl || "admin.gridevo.com";
  //     } else {
  //       domain = user?.isNewAdmin
  //         ? "test.gridevo.com"
  //         : "admintest.gridevo.com";
  //     }

  //     user.redirectUrl = user?.isNewAdmin
  //       ? `https://${domain}/admin/`
  //       : `https://${domain}`;
  //   }
  //   return res.status(200).json({ message: "Success", user });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError("user/whitelabel/edit")
  // public async whiteLabelEdit(request: RequestType<Request>, res: Response) {
  //   const user = await new DbUser().fromId(request.account.id);
  //   if (!user.whitelabelId) {
  //     throw ClientException.new({
  //       code: Code.BAD_REQUEST_ERROR,
  //       overrideMessage: "Please buy this feature first.",
  //     });
  //   }
  //   const oldDomain = user.domainUrl;
  //   const oldEmail = user.whitelabelEmail;

  //   user.domainUrl =
  //     request.body.domainUrl == "null" || request.body.domainUrl.length === 0
  //       ? null
  //       : request.body.domainUrl.toLowerCase();
  //   user.whitelabelEmail =
  //     request.body.whitelabelEmail == "null" ||
  //     request.body.whitelabelEmail.length === 0
  //       ? null
  //       : request.body.whitelabelEmail.toLowerCase();
  //   user.primaryColor = request.body.primaryColor;
  //   user.secondaryColor = request.body.secondaryColor;
  //   user.website = request.body.website == "null" ? null : request.body.website;
  //   user.helpCenterUrl =
  //     request.body.helpCenterUrl == "null" ? null : request.body.helpCenterUrl;
  //   user.supportWidgetUrl =
  //     request.body.supportWidgetUrl == "null"
  //       ? null
  //       : request.body.supportWidgetUrl;
  //   user.hostUrl = request.body.hostUrl == "null" ? null : request.body.hostUrl;
  //   user.hostUser =
  //     request.body.hostUser == "null" ? null : request.body.hostUser;
  //   user.hostPassword =
  //     request.body.hostPassword == "null" ? null : request.body.hostPassword;

  //   if (user.domainUrl) {
  //     user.domainUrl = user.domainUrl
  //       .trim()
  //       .toLowerCase()
  //       .replace(/^http:\/\/|^https:\/\//, "")
  //       .replace(/^www\./, "")
  //       .replace(/\/(.*)/, "");
  //   }

  //   // req.files is defined below - at upload.fields(...)
  //   // It can only expect 'logo' and 'favicon' files

  //   if (request.files?.logo) {
  //     const logo = request.files.logo[0];
  //     await this.s3Queue.upload(`logo.png`, logo.path, `users/${user.id}`, {
  //       optimize: false,
  //       convertTo: "png",
  //     });
  //   }

  //   if (request.files?.favicon) {
  //     const favicon = request.files.favicon[0];
  //     await this.s3Queue.upload(
  //       `favicon.png`,
  //       favicon.path,
  //       `users/${user.id}`,
  //       {
  //         optimize: false,
  //         convertTo: "png",
  //       }
  //     );
  //   }

  //   await user.save();
  //   let domainChanged = false;
  //   if (
  //     (user.domainUrl && user.domainUrl != oldDomain) ||
  //     (user.whitelabelEmail && user.whitelabelEmail != oldEmail)
  //   ) {
  //     domainChanged = true;
  //     this.emailQueue
  //       .sendEmail(
  //         "setupDomain",
  //         process.env.GRIDEVO_EMAIL,
  //         "<EMAIL>",
  //         {
  //           domain: user.domainUrl,
  //           whitelabelEmail: user.whitelabelEmail,
  //           id: user.id,
  //           email: user.email,
  //         }
  //       )
  //       .catch((error: any) => {
  //         logger.error(
  //           `Send email(setupDomain) in user/whitelabel/edit: ${error.message}`
  //         );
  //       });
  //   }

  //   if (request.body.hostingUpdates === "true") {
  //     await this.emailQueue.sendEmail(
  //       "sendHostingUpdate",
  //       user.email,
  //       "<EMAIL>",
  //       {
  //         ...pick(request.body, ["hostUrl", "hostUser", "hostPassword"]),
  //         useremail: user.email,
  //       }
  //     );
  //   }
  //   logger.info(`user/whitelabel/edit: id ${user.id}`);

  //   return res.status(200).json({ message: "Success", user, domainChanged });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError("user/delete")
  // public async delete(request: RequestType<Request>, res: Response) {
  //   await this.controller.delete(request.body.id, request.account);
  //   return res.status(200).json({ message: "Success" });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin])
  // @handleError("user/getAll")
  // public async getAll(request: RequestType<Request>, res: Response) {
  //   const users = await this.controller.getAllWithTours(
  //     request.query.page,
  //     request.account
  //   );
  //   return res.status(200).json({ message: "Success", users });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin])
  // @handleError("user/getAllWithTours")
  // public async getAllWithTours(request: RequestType<Request>, res: Response) {
  //   const users = await this.controller.getAllWithTours(
  //     request.query.page,
  //     request.account
  //   );
  //   return res.status(200).json({ message: "Success", users });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin])
  // @handleError(() => `user/search`)
  // public async search(request: RequestType<Request>, res: Response) {
  //   const { type, id } = request.account;
  //   if (![UserType.Admin, UserType.SuperAdmin].includes(type)) {
  //     throw ClientException.new({
  //       code: Code.NOT_FOUND,
  //     });
  //   }

  //   const filter = JSON.parse(request.body.filter) || {};
  //   const response = await new DbUser().search({
  //     ...request.body,
  //     filter,
  //     adminId: type === UserType.Admin ? id : null,
  //     refer: type === UserType.Admin ? id : null,
  //   });

  //   // response.data = response.data.map(({ tours, ...user }) => ({
  //   //   ...user,
  //   //   countTour: tours?.length,
  //   //   countPaid: tours?.reduce(
  //   //     (acc, tour) => (tour.subscriptionId ? ++acc : acc),
  //   //     0
  //   //   ),
  //   //   tourIds: filter.link ? tours.map((tour) => tour.id) : [],
  //   // }));
  //   return res.status(200).json({
  //     message: "Success",
  //     users: response,
  //     didSearchByTourLink: !!filter.link,
  //   });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError((request) => `user/${request.body.id}`)
  // public async get(request: RequestType<Request>, res: Response) {
  //   const user = await this.controller.get(
  //     request.body.id,
  //     request.account,
  //     !!request.isNewAdmin
  //   ); // TODO need to be changed after old crm is gone
  //   return res.status(200).json({ message: "Success", user });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin])
  // @handleError((request) => `user/getDNSRecords/${request.body.id}`)
  // public async getDNSRecords(request: RequestType<Request>, res: Response) {
  //   const dnsRecords = await this.controller.getDNSRecords(
  //     request.body.id,
  //     request.account
  //   );
  //   return res.status(200).json({ message: "Success", dnsRecords });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin])
  // @handleError((request) => `user/removeDNSRecords/${request.body.id}`)
  // public async removeDNSRecords(request: RequestType<Request>, res: Response) {
  //   await this.controller.removeDNSRecords(request.body.id, request.account);
  //   return res.status(200).json({ message: "Success" });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError((request) => `api/getUserAdmin/${request.body.id}`)
  // public async getUserAdmin(request: RequestType<Request>, res: Response) {
  //   const user = await this.controller.getUserAdmin(
  //     request.body.id,
  //     request.account
  //   );
  //   return res
  //     .status(200)
  //     .json(CoreApiResponse.success(GetUserAdminResponse.mapFrom(user.admin)));
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError(`user/getCurrent`)
  // public async getCurrent(request: RequestType<Request>, res: Response) {
  //   // TODO need to be changed after old crm is gone
  //   // req.account.selfId seems more reliable, because in superAdmin we were getting some errors when using req.account.id
  //   const user = await new DbUser().fromId(
  //     request.account.selfId,
  //     request.isNewAdmin
  //   );
  //   const translations = request.isNewAdmin
  //     ? await new DbAdminTranslation().getByUser(user.id)
  //     : null;

  //   const userLoginDesignSetting = user?.userLoginDesignSetting?.userId
  //     ? user?.userLoginDesignSetting
  //     : await new DbDefaultLoginDesignSettings().get();

  //   user.userLoginDesignSetting = userLoginDesignSetting;

  //   if (user?.userLoginDesignSetting?.logoUrl) {
  //     const loginLogoUrl = getLoginLogoURL(user);
  //     user.userLoginDesignSetting.logoUrl = loginLogoUrl;
  //   }
  //   if (user?.userLoginDesignSetting?.backgroundUrl) {
  //     const backgroundUrl = getLoginBackgroundURL(user);
  //     user.userLoginDesignSetting.backgroundUrl = backgroundUrl;
  //   }

  //   user.translations = translations?.translations
  //     ? typeof translations?.translations === "string"
  //       ? JSON.parse(translations?.translations)
  //       : translations?.translations
  //     : {};
  //   delete user.password;

  //   if (process.env.NODE_ENV != "local") {
  //     let domain = "";

  //     if (user?.domainUrl) {
  //       domain = user.domainUrl;
  //     }

  //     if (process.env.NODE_ENV == "production") {
  //       domain = user?.isNewAdmin
  //         ? user.domainUrl || "my.gridevo.com"
  //         : user.adminUrl || "admin.gridevo.com";
  //     } else {
  //       domain = user?.isNewAdmin
  //         ? "test.gridevo.com"
  //         : "admintest.gridevo.com";
  //     }

  //     user.redirectUrl = user?.isNewAdmin
  //       ? `https://${domain}/admin/`
  //       : `https://${domain}`;
  //   }

  //   logger.info(`user/getCurrent: id ${user.id}`);
  //   delete user?.ssoConfig?.credentials?.clientSecret;
  //   return res.status(200).json({ message: "Success", user });
  // }

  // // this endpoint is not related to hub credits
  // // it's related to the credits that the user can use to buy addons
  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError(`user/getCredits`)
  // public async getCredits(request: RequestType<Request>, res: Response) {
  //   const userId = request.body.id;
  //   const addon = request.body.addon;

  //   if (!userId) {
  //     throw ClientException.new({
  //       overrideMessage: "No user id provided",
  //       code: Code.BAD_REQUEST_ERROR,
  //     });
  //   }

  //   const credits = await this.controller.getUserCredits(userId, addon);

  //   logger.info(`user/getCredits: id ${userId}`);
  //   return res.status(200).json({ message: "Success", data: { credits } });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError(`user/changeEmail`)
  // public async changeEmail(request: RequestType<Request>, res: Response) {
  //   const user = await new DbUser().fromId(request.account.id);
  //   user.confirmed = false;
  //   user.tmpEmail = request.body.email.toLowerCase();
  //   user.emailToken = uuid();
  //   await user.save();
  //   this.emailQueue
  //     .sendEmail("confirmEmail", process.env.GRIDEVO_EMAIL, user.tmpEmail, {
  //       name: user.name,
  //       token: user.emailToken,
  //     })
  //     .catch((error: any) => {
  //       logger.error(
  //         `Send email(confirmEmail) in user/changeEmail: ${error.message}`
  //       );
  //     });
  //   logger.info(`user/changeEmail: id ${user.id}`);
  //   return res.status(200).json({ message: "Success", user });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin])
  // @handleError(`user/confirmEmail`)
  // public async confirmEmail(request: RequestType<Request>, res: Response) {
  //   const user = await new DbUser().fromId(request.body.id);

  //   if (
  //     request.account.type != UserType.SuperAdmin &&
  //     request.account.id != user.adminId
  //   ) {
  //     throw ClientException.new({
  //       overrideMessage: "Not allowed",
  //       code: Code.BAD_REQUEST_ERROR,
  //     });
  //   }
  //   if (!user.stripeId) {
  //     user.stripeId = await this.stripe.createCustomer({
  //       name: user.name,
  //       email: user.email,
  //       phone: user.phone,
  //     });
  //   }
  //   user.confirmed = true;
  //   if (user.newsletterEnabled) {
  //     await ((await alreadyRegistered(user.email))
  //       ? subscribeMailchimp(user.email, user.name)
  //       : addMailchimp(user.email, user.name));
  //   }
  //   user.save();
  //   logger.info(`user/confirmEmail: id ${user.id}`);
  //   return res.status(200).json({ message: "Success" });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError(`user/changePassword`)
  // public async changePassword(request: RequestType<Request>, res: Response) {
  //   const user = await new DbUser().fromId(request.account.id);

  //   const passwordsMatch = passwordHash.verify(
  //     request.body.oldPassword,
  //     user.password
  //   );
  //   if (!passwordsMatch) {
  //     return res.status(442).json({ message: "Wrong old password" });
  //   }
  //   user.password = passwordHash.generate(request.body.password);

  //   await user.save();

  //   this.emailQueue
  //     .sendEmail("passwordChanged", process.env.GRIDEVO_EMAIL, user.email, {
  //       name: user.name,
  //     })
  //     .catch((error: any) => {
  //       logger.error(
  //         `Send email(passwordChanged) in user/changePassword: ${error.message}`
  //       );
  //     });
  //   logger.info(`user/changePassword: id ${user.id}`);
  //   return res.status(200).json({ message: "Success", user });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError(`user/changeAnalytics`)
  // public async changeAnalytics(request: RequestType<Request>, res: Response) {
  //   const user = await new DbUser().fromId(request.account.id);
  //   user.googleViewId = request.body.googleViewId;
  //   user.fbPixel = request.body.fbPixel;
  //   user.fbAppId = request.body.fbAppId;

  //   await user.save();
  //   logger.info(`user/changeAnalytics: id ${user.id}`);
  //   return res.status(200).json({ message: "Success", user });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError(`user/changeApiKey`)
  // public async changeApiKey(request: RequestType<Request>, res: Response) {
  //   const user = await new DbUser().fromId(request.account.id);
  //   const { apiKey, mpTokenId, mpTokenSecret } = request.body;
  //   user.apiKey = apiKey;
  //   user.mpTokenId = mpTokenId;
  //   user.mpTokenSecret = mpTokenSecret;
  //   await user.save();
  //   logger.info(`user/changeApiKey: id ${user.id}`);
  //   return res.status(200).json({ message: "Success", user });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError(`user/generateApiKey`)
  // public async generateApiKey(request: RequestType<Request>, res: Response) {
  //   const apiKey = crypto.randomBytes(16).toString("hex");
  //   return res.status(200).json({ message: "Success", apiKey });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError(`user/getAvailablePayout`)
  // public async getAvailablePayout(
  //   request: RequestType<Request>,
  //   res: Response
  // ) {
  //   const payouts = await new DbPayout().getAvailablePayouts(
  //     request.account.id
  //   );
  //   return res.status(200).json({ message: "Success", payouts });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError(`user/createPayment`)
  // public async createPayment(request: RequestType<Request>, res: Response) {
  //   if (!request.body.currency)
  //     return res.status(422).json({ message: "Please choose currency" });
  //   if (!request.body.amount)
  //     return res.status(422).json({ message: "Please enter amount" });
  //   if (!request.body.paypalEmail)
  //     return res
  //       .status(422)
  //       .json({ message: "Please enter your PayPal email" });

  //   const availableAmount = await new DbPayout().getAvailablePayoutByCurrency(
  //     request.account.id,
  //     request.body.currency
  //   );
  //   const lastPayout = await new DbPayout().getLast(request.account.id);

  //   if (
  //     lastPayout &&
  //     moment().diff(moment(lastPayout.createdAt), "days") < 30
  //   ) {
  //     return res.status(422).json({
  //       message: `Payout can be made once a month. Your last payout was on ${moment(
  //         lastPayout.createdAt
  //       ).format("DD/MM/YYYY")}`,
  //     });
  //   }

  //   const requestedAmount = request.body.amount;
  //   if (availableAmount && requestedAmount > availableAmount.sum) {
  //     return res.status(422).json({
  //       message: `Sending more than ${availableAmount.sum} ${availableAmount.currency} not allowed`,
  //     });
  //   }

  //   const paypalResponse = await createPaypalPayout(
  //     requestedAmount,
  //     request.body.paypalEmail,
  //     request.body.currency
  //   );

  //   if (!paypalResponse.data.batch_header) {
  //     throw ClientException.new({
  //       code: Code.BAD_REQUEST_ERROR,
  //       overrideMessage: "Couldn't create the payment",
  //     });
  //   }
  //   const newPayout = new DbPayout();
  //   newPayout.amount = requestedAmount;
  //   newPayout.email = request.body.paypalEmail;
  //   newPayout.currency = request.body.currency;
  //   newPayout.paypalId = paypalResponse.data.batch_header.payout_batch_id;
  //   await newPayout.save(request.account.id);
  //   const payouts = await new DbPayout().getAvailablePayouts(
  //     request.account.id
  //   );
  //   logger.info(`user/createPayment: payout id: ${newPayout.id}`);
  //   return res.status(200).json({ message: "Success", payouts });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError(`user/removeNewsletter`)
  // public async removeNewsletter(request: RequestType<Request>, res: Response) {
  //   const user = await new DbUser().fromId(request.account.id);

  //   await unsubscribeMailchimp(user.email);
  //   user.newsletterEnabled = false;
  //   await user.save();
  //   logger.info(`user/removeNewsletter: id ${user.id}`);

  //   return res.status(200).json({ message: "Success", user });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError(`user/addNewsletter`)
  // public async addNewsletter(request: RequestType<Request>, res: Response) {
  //   const user = await new DbUser().fromId(request.account.id);
  //   await subscribeMailchimp(user.email, user.name);
  //   user.newsletterEnabled = true;
  //   await user.save();
  //   logger.info(`user/addNewsletter: id ${user.id}`);

  //   return res.status(200).json({ message: "Success", user });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User, Role.Client, Role.Agent])
  // @handleError(`user/getSubscription`)
  // public async getSubscription(request: RequestType<Request>, res: Response) {
  //   const user = await new DbUser().fromId(request.account.id);
  //   return res
  //     .status(200)
  //     .json({ message: "Success", subscription: user.subscription });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin])
  // @handleError(`user/whitelabel/setup`)
  // public async setupWhitelabel(request: RequestType<Request>, res: Response) {
  //   const user = await new DbUser().fromId(request.body.userId);

  //   new Promise(async (resolve: any, reject: any) => {
  //     try {
  //       const certificate: any = await this.awsQueue.createCertificate(
  //         user.domainUrl
  //       );
  //       const distribution: any = await this.awsQueue.createDistribution();

  //       this.emailQueue
  //         .sendEmail("confirmDomain", process.env.GRIDEVO_EMAIL, user.email, {
  //           name: user.name,
  //           records: [
  //             certificate,
  //             {
  //               host: user.domainUrl.split(".")[0],
  //               type: "CNAME",
  //               value: distribution.DomainName + ".",
  //             },
  //           ],
  //         })
  //         .catch((error: any) => {
  //           logger.error(
  //             `Send email(confirmDomain) in user/whitelabel/setup: ${error.message}`
  //           );
  //         });

  //       user.whitelabelConfig = WhitelabelConfig.Pending;
  //       user.cloudfrontId = distribution.Id;
  //       user.mpSDK = request.body.mpSDK || process.env.MATTERPORT_SDK;
  //       await user.save();
  //       return resolve();
  //     } catch (error) {
  //       return reject(error);
  //     }
  //   }).catch((error: any) => {
  //     this.emailQueue.sendEmail(
  //       "dnsError",
  //       process.env.GRIDEVO_EMAIL,
  //       "<EMAIL>",
  //       {
  //         id: user.id,
  //         name: user.name,
  //         email: user.email,
  //         domainUrl: user.domainUrl,
  //         whitelabelEmail: user.whitelabelEmail,
  //         error: error.message,
  //       }
  //     );
  //   });

  //   logger.info(`user/whitelabel/setup: ${user.domainUrl}`);
  //   return res.status(200).json({ message: "Success" });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin])
  // @handleError(`user/getTourCSV`)
  // public async getTourCSV(request: RequestType<Request>, res: Response) {
  //   const { type, id } = request.account;
  //   const adminId = request.body?.adminId || id;
  //   const users = await new DbUser().getTourCSV({
  //     adminId:
  //       type === (UserType.Admin || UserType.SuperAdmin) ? adminId : null,
  //     refer: type === (UserType.Admin || UserType.SuperAdmin) ? adminId : null,
  //   });

  //   const headers: string[] = [];
  //   if (users.length > 0) {
  //     Object.keys(users[0].toJSON()).forEach((header: string) => {
  //       headers.push(header);
  //     });

  //     headers.pop();

  //     const csvStream = format({
  //       headers: [
  //         ...headers,
  //         "count",
  //         "ecommerce count",
  //         "experience count",
  //         "ticketing count",
  //         "paid",
  //       ],
  //     });
  //     csvStream.pipe(res).on("end", () => {
  //       res.header({
  //         "Content-Type": "text/csv;base64",
  //         "Content-Disposition": "attachment; filename=Tour.csv",
  //       });
  //       res.end();
  //     });

  //     users.forEach((user: any) => {
  //       const writeData: any[] = [];
  //       const data = user.toJSON();
  //       const tours = user.tours;
  //       let ecommerce = 0;
  //       let experience = 0;
  //       let ticketing = 0;
  //       let count: number = 0;

  //       Object.keys(data).forEach((field: string) => {
  //         if (field !== "tours") writeData.push(data[field]);
  //       });

  //       tours.forEach((t: any) => {
  //         if (t.mode === "ecommerce") ecommerce++;
  //         if (t.gameId) experience++;

  //         ticketing +=
  //           (t?.ticketingToursByYourselves?.length || 0) +
  //           (t?.ticketingGameTours?.length || 0) +
  //           (t?.ticketingGuidedTours?.length || 0);
  //       });

  //       writeData.push(tours.length);

  //       Object.keys(tours).forEach((tour: string) => {
  //         if (tours[tour].subscriptionId) count++;
  //       });
  //       writeData.push(ecommerce, experience, ticketing, count);
  //       csvStream.write(writeData);
  //     });

  //     csvStream.end();
  //   }
  // }

  // @authorize([Role.SuperAdmin, Role.Admin])
  // @validate(IdParameter, { type: REQUEST_TYPE.PARAMS })
  // @validate(UpdatePermissionRequest)
  // @handleError(`admin/user/:id/permissions`)
  // public async updatePermissions(request: RequestType<Request>, res: Response) {
  //   const response = await this.controller.updatePermissions(
  //     request.params.id,
  //     {
  //       account: request.account,
  //       permissions: request.body.permissions,
  //       isNewAdmin: !!request.isNewAdmin, // TODO need to be changed after old crm is gone
  //     }
  //   );
  //   return res.json(CoreApiResponse.success(response));
  // }

  // @authorize([Role.User])
  // @handleError(`admin/user/permissions`)
  // public async getPermissions(request: RequestType<Request>, res: Response) {
  //   const response = await this.controller.getPermissions(request.account.id);
  //   return res.json(CoreApiResponse.success(response));
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError(`whitelabel/survey/edit`)
  // public async editWhitelabelSurvey(
  //   request: RequestType<Request>,
  //   res: Response
  // ) {
  //   const user = await new DbUser().fromId(request.account.id);
  //   await this.emailQueue.sendEmail(
  //     "whitelabelSurvey",
  //     user.email,
  //     process.env.GRIDEVO_EMAIL,
  //     {
  //       ...request.body,
  //       useremail: user.email,
  //     }
  //   );

  //   logger.info(`/whitelabel/survey/send`);
  //   return res.status(200).json({ message: "Success" });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError(`oauth/connect`)
  // public async connectOAuth(request: RequestType<Request>, res: Response) {
  //   const user = await new DbUser().fromId(request.account.id);
  //   const response = await requestHelper.fetch(
  //     "https://api.matterport.com/api/oauth/token",
  //     {
  //       method: "POST",
  //       headers: { "content-type": "application/x-www-form-urlencoded" },
  //       data: {
  //         grant_type: "authorization_code",
  //         code: request.body.code,
  //         scope:
  //           "ViewPublic ViewDetails EditDetails DownloadAssets PurchaseAssets",
  //         client_id: process.env.MP_OAUTH_ID,
  //         client_secret: process.env.MP_OAUTH_SECRET,
  //       },
  //     }
  //   );
  //   user.oauthToken = response.data?.access_token;
  //   user.oauthRefresh = response.data?.refresh_token;
  //   user.oauthExpire = moment(
  //     moment().unix() * 1000 + response.data?.expires_in * 1000
  //   ).toDate();
  //   user.oauth = true;
  //   await user.save();
  //   logger.info(`oauth/connect ${user.id}`);
  //   return res.status(200).json({ message: "Success" });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError(`oauth/asset/buy`)
  // public async buyAsset(request: RequestType<Request>, res: Response) {
  //   const user = await new DbUser().fromId(request.account.id);
  //   const mpController = new MatterportController(user);
  //   const availability = await mpController.buyAsset(
  //     request.body.matterId,
  //     request.body.bundleId
  //   );
  //   logger.info(
  //     `oauth/asset/buy ${user.id} ${request.body.matterId} ${request.body.bundleId}`
  //   );
  //   return res.status(200).json({ message: "Success", availability });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError(`oauth/asset/download`)
  // public async downloadAsset(request: RequestType<Request>, res: Response) {
  //   const user = await new DbUser().fromId(request.account.id);
  //   const mpController = new MatterportController(user);
  //   const asset = await mpController.downloadAsset(
  //     request.body.matterId,
  //     request.body.bundleId
  //   );
  //   return res.status(200).json({ message: "Success", asset });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError(`oauth/refresh`)
  // public async refreshOAuth(request: RequestType<Request>, res: Response) {
  //   const user = await new DbUser().fromId(request.account.id);
  //   const mpController = new MatterportController(user);
  //   const oauth: any = await mpController.refreshOAuth().catch((error) => {
  //     logger.error(error.message);
  //     return false;
  //   });
  //   logger.info(`oauth/connect ${user.id}`);
  //   return res.status(200).json({ message: "Success", oauth });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError(`oauth/disconnect`)
  // public async disconnectOAuth(request: RequestType<Request>, res: Response) {
  //   const user = await new DbUser().fromId(request.account.id);
  //   user.oauthToken = null;
  //   user.oauthRefresh = null;
  //   user.oauthExpire = null;
  //   user.oauth = false;
  //   await user.save();
  //   logger.info(`oauth/disconnect ${user.id}`);
  //   return res.status(200).json({ message: "Success" });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User, Role.Client])
  // @handleError(`schedule/analytics`)
  // public async scheduleAnalytics(request: RequestType<Request>, res: Response) {
  //   const uuidValue = uuid();
  //   const saveObject = {
  //     ...request.body,
  //     uuid: uuidValue,
  //     email: request.body.isEmailScheduled ? request.body.email : "",
  //     frequency: request.body.isEmailScheduled ? request.body.frequency : "",
  //     publicLink: request.body.publicLink.replace(/[^/]*$/, uuidValue),
  //     isPublic: request.body.isPublic || request.body.isEmailScheduled,
  //   };

  //   const scheduledAnalytics = await new DbScheduledAnalytics().createOrSave(
  //     request.account.id,
  //     saveObject
  //   );
  //   return res.status(200).json({ message: "Success", scheduledAnalytics });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError(`download/analytics`)
  // public async downloadAnalytics(request: RequestType<Request>, res: Response) {
  //   const csvContents = request.body.data;
  //   const fileName = `analytics_${uuid()}.xlsx`;
  //   const filePath = path.join(uploadPath, "uploads", fileName);

  //   // Create a new workbook
  //   const workbook = xlsx.utils.book_new();

  //   for (const csvContent of csvContents) {
  //     if (csvContent) {
  //       // Parse CSV content into a workbook
  //       const csvWorkbook = xlsx.read(csvContent.text, { type: "string" });

  //       // Get the first sheet from CSV workbook
  //       const csvSheetName = csvWorkbook.SheetNames[0];
  //       const csvWorksheet = csvWorkbook.Sheets[csvSheetName];

  //       // Append the worksheet to the workbook with the given name
  //       xlsx.utils.book_append_sheet(workbook, csvWorksheet, csvContent.name);
  //     }
  //   }

  //   // Write workbook to a buffer
  //   xlsx.writeFile(workbook, filePath);

  //   res.setHeader(
  //     "Content-Type",
  //     "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  //   );
  //   res.download(filePath, fileName, async (error) => {
  //     if (error) {
  //       logger.error(error);
  //     }
  //   });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @validate(UserLoginSettingsRequest)
  // @handleError(`user/saveCustomLoginSettings`)
  // public async saveCustomLoginSettings(
  //   request: RequestType<Request>,
  //   res: Response
  // ) {
  //   await this.controller.saveCustomLoginSettings(
  //     request.params.userId,
  //     request.body,
  //     request.files
  //   );
  //   return res.status(200).json({ message: "Success" });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError(`user/getCustomLoginSettings`)
  // public async getCustomLoginSettings(
  //   request: RequestType<Request>,
  //   res: Response
  // ) {
  //   const userId = request.params.userId;
  //   const settings = await new DbUserLoginDesignSettings().get({
  //     query: { userId },
  //     disableCatchError: true,
  //   });
  //   return res.status(200).json({
  //     message: "Success",
  //     data: UserLoginSettingsResponse.mapFrom(settings),
  //   });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError(`user/customLoginSettings/reset`)
  // public async resetLoginSettings(
  //   request: RequestType<Request>,
  //   res: Response
  // ) {
  //   const userId = request.params.userId;
  //   await new DbUserLoginDesignSettings().resetToDefault(userId);
  //   return res.status(200).json({ message: "Success" });
  // }

  /**
   * Take each handler, and attach to one of the Express.Router's
   * endpoints.
   */
  init() {
    //UserLoginDesignSettings
    // this.router.post(
    //   "/customLogin/settings/:userId",
    //   rateLimiter.middleware({
    //     duration: 15,
    //     points: 1,
    //     keyPrefix: "user-custom-login",
    //   }),
    //   upload(
    //     [
    //       { name: "logo", maxCount: 1 },
    //       { name: "backgroundImage", maxCount: 1 },
    //     ],
    //     {
    //       maxFileSize: 25,
    //       allowTypes: [mimeByGroup.IMAGE],
    //     }
    //   ),
    //   this.saveCustomLoginSettings.bind(this)
    // );
    // this.router.get(
    //   "/customLogin/settings/:userId",
    //   this.getCustomLoginSettings.bind(this)
    // );
    // this.router.post(
    //   "/customLogin/settings/:userId/reset",
    //   this.resetLoginSettings.bind(this)
    // );
    // this.router.post("/add", this.add.bind(this));
    // this.router.post("/edit", upload("profile"), this.edit.bind(this));
    // this.router.post("/delete", this.delete.bind(this));
    // this.router.post("/updateHubspot", this.updateHubspot.bind(this));
    // this.router.post(
    //   "/createZendeskTicket",
    //   rateLimiter.middleware({
    //     duration: 1,
    //     points: 20,
    //     keyPrefix: "create-zendesk-ticket",
    //   }),
    //   upload([{ name: "files", maxCount: 5 }], {
    //     maxFileSize: 5,
    //     allowTypes: [mimeByGroup.IMAGE, mimeByGroup.VIDEO, mimeByGroup.JSON],
    //   }),
    //   this.createZendeskTicket.bind(this)
    // );
    // this.router.post("/search", this.search.bind(this));
    // this.router.post("/getAll", this.getAll.bind(this));
    // this.router.post("/getAllWithTours", this.getAllWithTours.bind(this));
    // this.router.post("/get", this.get.bind(this));
    // this.router.post("/getDNSRecords", this.getDNSRecords.bind(this));
    // this.router.post("/removeDNSRecords", this.removeDNSRecords.bind(this));
    // this.router.post("/getUserAdmin", this.getUserAdmin.bind(this));
    // this.router.post("/getCurrent", this.getCurrent.bind(this));
    // this.router.post("/subscription/add", this.addSubscription.bind(this));
    // this.router.post("/subscription/edit", this.editSubscription.bind(this));
    // this.router.post(
    //   "/subscription/extendTrial",
    //   this.extendTrialSubscription.bind(this)
    // );
    // this.router.post(
    //   "/subscription/stopTrial",
    //   this.stopTrialSubscription.bind(this)
    // );
    // this.router.post(
    //   "/subscription/cancel",
    //   this.cancelSubscription.bind(this)
    // );
    // this.router.post(
    //   "/subscription/upgrade",
    //   this.upgradeSubscription.bind(this)
    // );
    // this.router.post(
    //   "/subscription/downgrade",
    //   this.downgradeSubscription.bind(this)
    // );
    // this.router.post("/removeNewsletter", this.removeNewsletter.bind(this));
    // this.router.post("/addNewsletter", this.addNewsletter.bind(this));
    // this.router.post("/getSubscription", this.getSubscription.bind(this));
    // this.router.post("/changeEmail", this.changeEmail.bind(this));
    // this.router.post("/getCredits", this.getCredits.bind(this));
    // this.router.post("/confirmEmail", this.confirmEmail.bind(this));
    // this.router.post("/changePassword", this.changePassword.bind(this));
    // this.router.post("/changeAnalytics", this.changeAnalytics.bind(this));
    // this.router.post("/getAvailablePayout", this.getAvailablePayout.bind(this));
    // this.router.post("/createPayment", this.createPayment.bind(this));
    // this.router.post("/changeApiKey", this.changeApiKey.bind(this));
    // this.router.get("/generateApiKey", this.generateApiKey.bind(this));
    // this.router.post("/oauth/connect", this.connectOAuth.bind(this));
    // this.router.get("/oauth/disconnect", this.disconnectOAuth.bind(this));
    // this.router.get("/oauth/refresh", this.refreshOAuth.bind(this));
    // this.router.post("/oauth/asset/buy", this.buyAsset.bind(this));
    // this.router.post("/oauth/asset/download", this.downloadAsset.bind(this));
    // this.router.post(
    //   "/whitelabel/edit",
    //   upload([
    //     { name: "logo", maxCount: 1 },
    //     { name: "favicon", maxCount: 1 },
    //   ]),
    //   this.whiteLabelEdit.bind(this)
    // );
    // this.router.post("/whitelabel/setup", this.setupWhitelabel.bind(this));
    // this.router.post("/whitelabel/pay", this.payWhitelabel.bind(this));
    // this.router.post(
    //   "/clientWhitelabel/pay",
    //   this.payClientWhitelabel.bind(this)
    // );
    // this.router.post("/whitelabel/cancel", this.cancelWhitelabel.bind(this));
    // this.router.post(
    //   "/clientWhitelabel/cancel",
    //   this.cancelClientWhitelabel.bind(this)
    // );
    // this.router.post("/credits/buy", this.buyCredits.bind(this));
    // this.router.post("/credits/transfer", this.transferHubMinutes.bind(this));
    // this.router.get("/getCannyToken", this.getCannyToken.bind(this));
    // this.router.get("/getTourCSV", this.getTourCSV.bind(this));
    // this.router.post("/:id/permissions", this.updatePermissions.bind(this));
    // this.router.get("/permissions", this.getPermissions.bind(this));
    // this.router.post(
    //   "/whitelabel/survey/edit",
    //   this.editWhitelabelSurvey.bind(this)
    // );
    // this.router.post("/schedule/analytics", this.scheduleAnalytics.bind(this));
    // this.router.post("/download/analytics", this.downloadAnalytics.bind(this));
  }
}

export default UserRouter;
