import { Request, Response } from "express";
import * as moment from "moment";
import { requestHelper } from "src/helpers";
import { logger } from "src/log";

import { RequestType } from "../core/common";

const md5 = require("md5");
const https = require("node:https");

const getMailchimpInstance = (() => {
  let instance: any;

  return () => {
    if (!instance) {
      const Mailchimp = require("mailchimp-api-v3");
      instance = new Mailchimp(process.env.MAILCHIMP_API_KEY);
    }
    return instance;
  };
})();

export async function getInfoByIp(request: any) {
  if (!request || !process.env.IPCHECK) return null;
  const ip = request.headers["x-forwarded-for"] || request.ip;
  if (!request.body.skipIP && ip) {
    const { data: ipRes } = await requestHelper
      .fetch<{ country_code: string | number; country_name: string }>(
        `https://api.ipstack.com/${ip.split(",").pop().trim()}?access_key=${
          process.env.IPSTACK_KEY
        }`,
        {
          method: "GET",
          timeout: 500,
        }
      )
      .catch(() => {
        return null;
      });
    return {
      country_code: ipRes?.country_code,
      country_name: ipRes?.country_name,
    };
  }
  return null;
}

export const checkChina = (ipInfo: any) => ipInfo?.country_code === "CN";

// export async function getUserByURL(origin: string) {
//   //TODO: change to default ones... not getAll..
//   const [userLoginDesignSetting] =
//     await new DbDefaultLoginDesignSettings().getAll({
//       attributes: { exclude: ["id"] },
//     });

//   let user: any = {
//     //defaults
//     logo: null,
//     favicon: null,
//     whitelabel: false,
//     website: "https://gridevo.com",
//     primaryColor: "#292929",
//     secondaryColor: "#ea475c",
//     mpSDK: process.env.MATTERPORT_SDK,
//     userLoginDesignSetting,
//   };

//   if (!origin) return user;

//   const url = origin
//     .trim()
//     .toLowerCase()
//     .replace(/^http:\/\/|^https:\/\//, "")
//     .replace(/^www\./, "")
//     .replace(/\/(.*)/, "");

//   if (url === "my.gridevo.com" || url === "admin.gridevo.com") return user;

//   user = await new DbUser().fromDomainUrl(url);

//   //TODO: add the userLoginDesignSettings of this user here!

//   return user;
// }

// export function getLogoURL(u: User, client: Client) {
//   const user = u.admin?.id === 1 ? u : u.admin && !u.whitelabelId ? u.admin : u;
//   if (!user.whitelabelId) return null;
//   if (client && !client.whitelabelId) client = null;
//   const logoActive = client?.whitelabelLogo;
//   const type = logoActive ? "clients" : "users";
//   const id = logoActive ? client?.id : user.id;
//   const date = logoActive ? client?.updatedAt : user.updatedAt;
//   const v =
//     moment().diff(date, "days") == 0
//       ? "?v=" + moment(date).format("HHmmss")
//       : "";
//   return `${process.env.MEDIA_HOST_URL}/${type}/${id}/logo.png${v}`;
// }

// export function getClientsLogo(client: Client) {
//   if (client && !client.logo) return null;
//   const v =
//     moment().diff(client?.updatedAt, "days") == 0
//       ? "?v=" + moment(client?.updatedAt).format("HHmmss")
//       : "";
//   return `${process.env.MEDIA_HOST_URL}/clients/${client?.id}/logo.png${v}`;
// }

// export function getFaviconURL(user: User) {
//   if (!user?.whitelabelId)
//     return `${process.env.MEDIA_HOST_URL}/users/1/favicon.png`;
//   const v =
//     moment().diff(user.updatedAt, "days") == 0
//       ? "?v=" + moment(user.updatedAt).format("HHmmss")
//       : "";
//   return `${process.env.MEDIA_HOST_URL}/users/${user.id}/favicon.png${v}`;
// }

// export function getLoginLogoURL(user: User) {
//   if (!user?.whitelabelId)
//     return `${process.env.MEDIA_HOST_URL}/users/1/${user.userLoginDesignSetting.logoUrl}`;
//   const v =
//     moment().diff(user.updatedAt, "days") == 0
//       ? "?v=" + moment(user.updatedAt).format("HHmmss")
//       : "";
//   return `${process.env.MEDIA_HOST_URL}/users/${user.id}/${user.userLoginDesignSetting.logoUrl}${v}`;
// }

// export function getLoginBackgroundURL(user: User) {
//   if (!user?.whitelabelId)
//     return `${process.env.MEDIA_HOST_URL}/users/1/${user.userLoginDesignSetting.backgroundUrl}`;
//   const v =
//     moment().diff(user.updatedAt, "days") == 0
//       ? "?v=" + moment(user.updatedAt).format("HHmmss")
//       : "";
//   return `${process.env.MEDIA_HOST_URL}/users/${user.id}/${user.userLoginDesignSetting.backgroundUrl}${v}`;
// }

// export function getLoginImagesURL(user: User) {
//   if (!user?.whitelabelId) {
//     return `${process.env.MEDIA_HOST_URL}/users/1/logo.png`;
//   }
//   const urlString = `${process.env.MEDIA_HOST_URL}/users/${user.id}/`;
//   if (user.userLoginDesignSetting?.backgroundUrl) {
//     user.userLoginDesignSetting.backgroundUrl = urlString.concat(
//       user.userLoginDesignSetting.backgroundUrl
//     );
//   }
//   if (user.userLoginDesignSetting?.logoUrl) {
//     user.userLoginDesignSetting.logoUrl = urlString.concat(
//       user.userLoginDesignSetting?.logoUrl
//     );
//   }

//   return user;
// }

// export async function getPaypalToken() {
//   return requestHelper
//     .fetch(`${process.env.PAYPAL_API}/v1/oauth2/token`, {
//       method: "POST",
//       headers: {
//         Accept: "application/json",
//         "Accept-Language": "en_US",
//         "content-type": "application/x-www-form-urlencoded",
//       },
//       data: {
//         grant_type: "client_credentials",
//       },
//       auth: {
//         username: process.env.PAYPAL_CLIENT,
//         password: process.env.PAYPAL_SECRET,
//       },
//     })
//     .then(({ data: res }) => {
//       return res.access_token;
//     })
//     .catch((error) => {
//       throw error;
//     });
// }

// export async function getPaypalSubscription(id: string) {
//   const token = await getPaypalToken();

//   return requestHelper
//     .fetch(`${process.env.PAYPAL_API}/v1/billing/subscriptions/${id}`, {
//       method: "GET",
//       headers: {
//         Authorization: `Bearer ${token}`,
//       },
//     })
//     .catch((error: any) => {
//       throw error;
//     });
// }

// export async function cancelPaypalSubscription(id: string) {
//   const token = await getPaypalToken();

//   return requestHelper
//     .fetch(`${process.env.PAYPAL_API}/v1/billing/subscriptions/${id}/cancel`, {
//       method: "POST",
//       headers: {
//         Authorization: `Bearer ${token}`,
//       },
//       data: {
//         reason: "Cancaled through gridevo",
//       },
//     })
//     .catch((error: any) => {
//       throw error;
//     });
// }

// export async function createPaypalPayout(
//   amount: string,
//   email: string,
//   currency: string
// ) {
//   const token = await getPaypalToken();

//   return requestHelper
//     .fetch(`${process.env.PAYPAL_API}/v1/payments/payouts`, {
//       method: "POST",
//       headers: {
//         Authorization: `Bearer ${token}`,
//       },
//       data: {
//         sender_batch_header: {
//           sender_batch_id: `Payout_${new Date().toISOString()}`,
//           email_subject: "You have a payout!",
//           email_message:
//             "You have received a payout! Thanks for using our service!",
//         },
//         items: [
//           {
//             recipient_type: "EMAIL",
//             amount: {
//               value: amount,
//               currency: currency,
//             },
//             receiver: email,
//           },
//         ],
//       },
//     })
//     .catch((error: any) => {
//       throw error;
//     });
// }

// export async function addMailchimp(email: string, name: string) {
//   const mailchimp = getMailchimpInstance();
//   return mailchimp
//     .post(`/lists/${process.env.MAILCHIMP_LIST_KEY}/members`, {
//       email_address: email,
//       status: "subscribed",
//       merge_fields: {
//         FNAME: name,
//       },
//     })
//     .then((results) => {
//       logger.info(`newsletter subscribe | status: ${results.statusCode}`);
//     })
//     .catch((error) => {
//       logger.error(`newsletter subscribe | message: ${error.title}`);
//       throw error;
//     });
// }

// export async function alreadyRegistered(email: string) {
//   const id = md5(email);
//   const mailchimp = getMailchimpInstance();
//   return mailchimp
//     .get(`/lists/${process.env.MAILCHIMP_LIST_KEY}/members/${id}`)
//     .then((results) => {
//       if (results.statusCode === 200) {
//         return true;
//       }
//     })
//     .catch(() => {
//       return false;
//     });
// }

// export async function subscribeMailchimp(email: string, name: string) {
//   const id = md5(email);
//   const mailchimp = getMailchimpInstance();
//   return mailchimp
//     .put(`/lists/${process.env.MAILCHIMP_LIST_KEY}/members/${id}`, {
//       status: "subscribed",
//       merge_fields: {
//         FNAME: name,
//       },
//     })
//     .then((results) => {
//       logger.info(
//         `newsletter subscribe member | status: ${results.statusCode}`
//       );
//     })
//     .catch((error) => {
//       logger.error(`newsletter subscribe member | message: ${error.title}`);
//       throw error;
//     });
// }

// export async function unsubscribeMailchimp(email: string) {
//   const id = md5(email);
//   const mailchimp = getMailchimpInstance();
//   return mailchimp
//     .put(`/lists/${process.env.MAILCHIMP_LIST_KEY}/members/${id}`, {
//       email_address: email,
//       status: "unsubscribed",
//     })
//     .then((results) => {
//       logger.info(
//         `newsletter unsubscribe member | status: ${results.statusCode}`
//       );
//     })
//     .catch((error) => {
//       logger.error(`newsletter unsubscribe member | message: ${error.title}`);
//       throw error;
//     });
// }

// export async function getBusyTimeSlots(
//   request: RequestType<Request>,
//   res: Response
// ) {
//   try {
//     const slots = await new DbBusyTimeSlot().getAll(request.body.tourId);
//     return res.status(200).json({ message: "Success", slots });
//   } catch (error) {
//     logger.error(`public/getBusyTimeSlots:${error.dbMessage ?? error.message}`);
//     return res.status(error.status || 500).json({ message: error.message });
//   }
// }

// export async function addBusyTimeSlot(
//   request: RequestType<Request>,
//   res: Response
// ) {
//   try {
//     const slot = await new DbBusyTimeSlot();

//     slot.day = request.body.day;
//     slot.title = request.body.title ? request.body.title : null;
//     const today = moment().format("DD/MM/YYYY");
//     const start = moment.utc(
//       today + "T" + request.body.startTime,
//       "DD/MM/YYYYTHH:mm"
//     );
//     const end = moment.utc(
//       today + "T" + request.body.endTime,
//       "DD/MM/YYYYTHH:mm"
//     );
//     if (start.isDST()) start.subtract(1, "hour");
//     if (end.isDST()) end.subtract(1, "hour");

//     slot.startTime = start.format("HH:mm");
//     slot.endTime = end.format("HH:mm");

//     await slot.save(request.body.tourId);
//     return res.status(200).json({ message: "Success", slot });
//   } catch (error) {
//     logger.error(`public/addBusyTimeSlot:${error.dbMessage ?? error.message}`);
//     return res.status(error.status || 500).json({ message: error.message });
//   }
// }

// export async function deleteAppointment(
//   request: RequestType<Request>,
//   res: Response
// ) {
//   try {
//     const appointment = await new DbAppointment().fromId(request.body.id);
//     await appointment.destroy();
//     return res.status(200).json({ message: "Success" });
//   } catch (error) {
//     logger.error(
//       `public/deleteAppointment:${error.dbMessage ?? error.message}`
//     );
//     return res.status(error.status || 500).json({ message: error.message });
//   }
// }
// export async function deleteBusyTimeSlot(
//   request: RequestType<Request>,
//   res: Response
// ) {
//   try {
//     const slot = await new DbBusyTimeSlot().fromId(request.body.id);
//     await slot.destroy();
//     return res.status(200).json({ message: "Success" });
//   } catch (error) {
//     logger.error(
//       `public/deleteBusyTimeSlot:${error.dbMessage ?? error.message}`
//     );
//     return res.status(error.status || 500).json({ message: error.message });
//   }
// }

// export async function getAppointments(
//   request: RequestType<Request>,
//   res: Response
// ) {
//   try {
//     const appointments = await new DbAppointment().getAll(request.body.tourId);
//     return res.status(200).json({ message: "Success", appointments });
//   } catch (error) {
//     logger.error(`public/getBusyTimeSlots:${error.dbMessage ?? error.message}`);
//     return res.status(error.status || 500).json({ message: error.message });
//   }
// }

// export async function getAppointmentsInterval(
//   request: RequestType<Request>,
//   res: Response
// ) {
//   const endeDate = new Date(request.body.endDate + "UTC");
//   endeDate.setDate(endeDate.getDate() + 1);
//   endeDate.setSeconds(endeDate.getSeconds() - 1);

//   try {
//     const appointments = await new DbAppointment().fromTimeInterval(
//       request.body.tourId,
//       new Date(request.body.startDate + "UTC"),
//       new Date(endeDate)
//     );
//     return res.status(200).json({ message: "Success", appointments });
//   } catch (error) {
//     logger.error(
//       `public/getAppointmentsInterval:${error.dbMessage ?? error.message}`
//     );
//     return res.status(error.status || 500).json({ message: error.message });
//   }
// }

// const currency = {
//   DE: { ISO: "EUR", SYM: "€" },
//   FR: { ISO: "EUR", SYM: "€" },
//   AT: { ISO: "EUR", SYM: "€" },
//   BE: { ISO: "EUR", SYM: "€" },
//   CY: { ISO: "EUR", SYM: "€" },
//   EE: { ISO: "EUR", SYM: "€" },
//   FI: { ISO: "EUR", SYM: "€" },
//   GR: { ISO: "EUR", SYM: "€" },
//   IE: { ISO: "EUR", SYM: "€" },
//   IT: { ISO: "EUR", SYM: "€" },
//   LV: { ISO: "EUR", SYM: "€" },
//   LT: { ISO: "EUR", SYM: "€" },
//   LU: { ISO: "EUR", SYM: "€" },
//   MT: { ISO: "EUR", SYM: "€" },
//   NL: { ISO: "EUR", SYM: "€" },
//   PT: { ISO: "EUR", SYM: "€" },
//   SK: { ISO: "EUR", SYM: "€" },
//   SI: { ISO: "EUR", SYM: "€" },
//   ES: { ISO: "EUR", SYM: "€" },
//   EZ: { ISO: "EUR", SYM: "€" },
//   GB: { ISO: "GBP", SYM: "£" },
//   US: { ISO: "USD", SYM: "$" },
// };
// const defaultCurrent = { ISO: "USD", SYM: "$" };

// export function getCurrency(country: string) {
//   let current = defaultCurrent;
//   if (currency[country]) current = currency[country];
//   return current;
// }

// export function getCurrencies() {
//   const allISO = Object.values(currency).map((x) => x.ISO);
//   const uniq = [...new Set(allISO)];
//   return uniq;
// }

// export async function checkIframe(url) {
//   try {
//     if (url.length > 1024 || !url.includes("https")) {
//       throw new Error("URL is invalid");
//     }

//     const httpsAgent = new https.Agent({ rejectUnauthorized: false });

//     const { headers: response } = await requestHelper.fetch(url, {
//       method: "GET",
//       headers: {
//         "User-Agent":
//           "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_10_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/44.0.2403.89 Safari/537.36",
//       },
//       httpsAgent,
//       timeout: 5000,
//     });

//     let header = response["x-frame-options"];

//     if (header) {
//       header = header.toUpperCase();
//       if (
//         header == "DENY" ||
//         header == "SAMEORIGIN" ||
//         header == "ALLOW-FROM"
//       ) {
//         const policy = response["content-security-policy"];
//         if (!policy?.includes("gridevo.com")) {
//           throw new Error(
//             "This website does not allow others to embed it in an iframe. If it's a video (e.g. YouTube), choose a video as type."
//           );
//         }
//       }
//     }
//   } catch (error) {
//     logger.error(`checkIframe: ${error.message}`);
//     throw error;
//   }
// }

// export function rgbToHex(rgb) {
//   // color util
//   const r = Number.parseInt(rgb.r);
//   const g = Number.parseInt(rgb.g);
//   const b = Number.parseInt(rgb.b);
//   return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
// }

// export function makeID(length: number) {
//   let result = "";
//   const characters =
//     "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
//   const charactersLength = characters.length;
//   for (let index = 0; index < length; index++) {
//     result += characters.charAt(Math.floor(Math.random() * charactersLength));
//   }
//   return result;
// }
