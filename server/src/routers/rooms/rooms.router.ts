import { Request, Response, Router } from "express";

import { handleError, RequestType, UserType } from "src/core/common";

import RoomsController from "src/controllers/rooms/roomsController";
// import authorize from "src/lib/permission";

class RoomsRouter {
  router: Router;

  roomsController: RoomsController;

  constructor() {
    this.router = Router();
    this.roomsController = new RoomsController({});
    this.init();
  }

  @handleError("rooms/addRoomCategory")
  public async addRoomCategory(request: RequestType<Request>, res: Response) {
    const categories = await this.roomsController.addRoomCategory(request.body);
    return res.status(200).json({ message: "Success", categories });
  }

  /**
   * Take each handler, and attach to one of the Express.Router's
   * endpoints.
   */
  init() {
    this.router.get(
      "/",
      this.roomsController.getCategoriesWithRooms.bind(this)
    );
    this.router.post("/add", this.addRoomCategory.bind(this));
  }
}

export default RoomsRouter;
