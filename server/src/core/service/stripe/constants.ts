export const EU_COUNTRIES = [
  'AT',
  'BE',
  'BG',
  'HR',
  'CY',
  'CZ',
  'DK',
  'EE',
  'FI',
  'FR',
  'DE',
  'EL',
  'HU',
  'IE',
  'IT',
  'LV',
  'LT',
  'LU',
  'MT',
  'NL',
  'PL',
  'PT',
  'RO',
  'SK',
  'SI',
  'ES',
  'SE',
  'XI',
];

const EU_VAT_REGEX = `^(${EU_COUNTRIES.join(
  '|',
)})([A-Z]{1,2}\\d{8,9}|\\d{8,14})$`;

export const TaxRegex = {
  ad_nrt: new RegExp('^[A-Z]-\\d{6}-[A-Z]$'), // Andorran NRT number
  ae_trn: new RegExp('^\\d{15}$'), // United Arab Emirates TRN
  ar_cuit: new RegExp('^\\d{2}-\\d{7,8}-\\d$'), // Argentinian tax ID number
  au_abn: new RegExp('^\\d{11}$'), // Australian Business Number
  au_arn: new RegExp('^\\d{12}$'), // Australian Taxation Office Reference Number
  bg_uic: new RegExp('^\\d{9}$'), // Bulgaria Unified Identification Code
  bh_vat: new RegExp('^\\d{9}$'), // Bahrain VAT
  bo_tin: new RegExp('^\\d{1,9}$'), // Bolivia TIN
  br_cnpj: new RegExp('^\\d{2}\\.\\d{3}\\.\\d{3}/\\d{4}-\\d{2}$'), // Brazilian CNPJ number
  br_cpf: new RegExp('^\\d{3}\\.\\d{3}\\.\\d{3}-\\d{2}$'), // Brazilian CPF number
  ca_bn: new RegExp('^\\d{9}$'), // Canadian BN
  ca_gst_hst: new RegExp('^\\d{9}RT\\d{4}$'), // Canadian GST/HST number
  ca_pst_bc: new RegExp('^PST-\\d{4}-\\d{4}$'), // Canadian PST number
  ca_pst_mb: new RegExp('^\\d{6}-\\d$'), // Canadian PST number
  ca_pst_sk: new RegExp('^\\d{7}$'), // Canadian PST number
  ca_qst: new RegExp('^\\d{10}TQ\\d{4}$'), // Canadian QST number
  ch_vat: new RegExp('^CHE-\\d{3}\\.\\d{3}\\.\\d{3} MWST$'), // Switzerland VAT number
  cl_tin: new RegExp('^\\d{1,2}\\.\\d{3}\\.\\d{3}-[A-K0-9]$'), // Chilean TIN
  cn_tin: new RegExp('^\\d{18}$'), // Chinese tax ID
  co_nit: new RegExp('^\\d{1,3}\\.\\d{3}\\.\\d{3}-\\d$'), // Colombian NIT number
  cr_tin: new RegExp('^\\d{1,3}-\\d{3,10}-\\d{1,3}$'), // Costa Rican tax ID
  do_rcn: new RegExp('^\\d{3}-\\d{7}-\\d$'), // Dominican RCN number
  ec_ruc: new RegExp('^\\d{10,13}$'), // Ecuadorian RUC number
  eg_tin: new RegExp('^\\d{14}$'), // Egypt TIN
  es_cif: new RegExp('^[A-Z]\\d{8}$'), // Spanish NIF number (previously Spanish CIF number)
  eu_oss_vat: new RegExp('^EU\\d{9}$'), // European One Stop Shop VAT number for non-Union scheme
  eu_vat: new RegExp(`${EU_VAT_REGEX}`), // European VAT number
  gb_vat: new RegExp('^GB\\d{9}$'), // United Kingdom VAT number
  ge_vat: new RegExp('^\\d{9}$'), // Georgian VAT
  hk_br: new RegExp('^\\d{8}$'), // Hong Kong BR number
  hu_tin: new RegExp('^\\d{8}-\\d-\\d{2}$'), // Hungary tax number
  id_npwp: new RegExp('^\\d{2}\\.\\d{3}\\.\\d{3}\\.\\d{1}-\\d{3}\\.\\d{3}$'), // Indonesian NPWP number
  il_vat: new RegExp('^\\d{9}$'), // Israel VAT
  in_gst: new RegExp('^\\d{2}[A-Z]{5}\\d{4}[A-Z]{1}\\d[Z]{1}\\d{3}$'), // Indian GST number
  is_vat: new RegExp('^\\d{6}$'), // Icelandic VAT
  jp_cn: new RegExp('^\\d{13}$'), // Japanese Corporate Number
  jp_rn: new RegExp('^\\d{5}$'), // Japanese Registered Foreign Businesses' Registration Number
  jp_trn: new RegExp('^\\d{10}$'), // Japan TRN
  ke_pin: new RegExp('^\\d{9}[A-Z]{1}$'), // Kenya PIN
  kr_brn: new RegExp('^\\d{3}-\\d{2}-\\d{5}$'), // Korean BRN
  kz_bin: new RegExp('^\\d{12}$'), // Kazakhstan BIN
  li_uid: new RegExp('^CHE\\d{9}(MWST|TVA)$'), // Liechtensteinian UID number
  mx_rfc: new RegExp('^[A-Z]{3,4}\\d{6}[A-Z0-9]{3}$'), // Mexican RFC number
  my_frp: new RegExp('^\\d{8}$'), // Malaysian FRP number
  my_itn: new RegExp('^[CP]{1}\\d{10}$'), // Malaysian ITN
  my_sst: new RegExp('^[A-Z]{1}\\d{2}-\\d{4}-\\d{8}$'), // Malaysian SST number
  ng_tin: new RegExp('^\\d{10}$'), // Nigerian TIN
  no_vat: new RegExp('^\\d{9}MVA$'), // Norwegian VAT number
  no_voec: new RegExp('^\\d{7}$'), // Norwegian VAT on e-commerce number
  nz_gst: new RegExp('^\\d{9}$'), // New Zealand GST number
  om_vat: new RegExp('^\\d{8}$'), // Oman VAT
  pe_ruc: new RegExp('^\\d{11}$'), // Peruvian RUC number
  ph_tin: new RegExp('^\\d{12}$'), // Philippines Tax Identification Number
  ro_tin: new RegExp('^\\d{13}$'), // Romanian tax ID number
  rs_pib: new RegExp('^\\d{8,9}$'), // Serbian PIB
  ru_inn: new RegExp('^\\d{10,12}$'), // Russian INN
  ru_kpp: new RegExp('^\\d{9}$'), // Russian KPP
  sa_vat: new RegExp('^\\d{15}$'), // Saudi Arabia VAT
  sg_gst: new RegExp('^[A-Z]\\d{7}[A-Z]$'), // Singaporean GST
  sg_uen: new RegExp('^\\d{9}[A-Z]{1}$'), // Singaporean UEN
  si_tin: new RegExp('^\\d{8}$'), // Slovenia tax number
  sv_nit: new RegExp('^\\d{14}$'), // El Salvador NIT
  th_vat: new RegExp('^\\d{13}$'), // Thai VAT
  tr_tin: new RegExp('^\\d{10}$'), // Turkish Tax Identification Number
  tw_vat: new RegExp('^\\d{8}$'), // Taiwanese VAT
  ua_vat: new RegExp('^\\d{12}$'), // Ukrainian VAT
  us_ein: new RegExp('^\\d{2}-\\d{7}$'), // United States EIN
  uy_ruc: new RegExp('^\\d{12}$'), // Uruguayan RUC number
  ve_rif: new RegExp('^[A-Z]-\\d{8}-\\d$'), // Venezuelan RIF number
  vn_tin: new RegExp('^\\d{10}$'), // Vietnamese tax ID number
  za_vat: new RegExp('^\\d{10}$'), // South African VAT number
};

//  https://docs.stripe.com/billing/customer/tax-ids
export const STRIPE_SUPPORTED_TYPES = [
  'ad_nrt',
  'ae_trn',
  'ar_cuit',
  'au_abn',
  'au_arn',
  'bg_uic',
  'bh_vat',
  'bo_tin',
  'br_cnpj',
  'br_cpf',
  'ca_bn',
  'ca_gst_hst',
  'ca_pst_bc',
  'ca_pst_mb',
  'ca_pst_sk',
  'ca_qst',
  'ch_vat',
  'cl_tin',
  'cn_tin',
  'co_nit',
  'cr_tin',
  'do_rcn',
  'ec_ruc',
  'eg_tin',
  'es_cif',
  'eu_oss_vat',
  'eu_vat',
  'gb_vat',
  'ge_vat',
  'hk_br',
  'hu_tin',
  'id_npwp',
  'il_vat',
  'in_gst',
  'is_vat',
  'jp_cn',
  'jp_rn',
  'jp_trn',
  'ke_pin',
  'kr_brn',
  'kz_bin',
  'li_uid',
  'mx_rfc',
  'my_frp',
  'my_itn',
  'my_sst',
  'ng_tin',
  'no_vat',
  'no_voec',
  'nz_gst',
  'om_vat',
  'pe_ruc',
  'ph_tin',
  'ro_tin',
  'rs_pib',
  'ru_inn',
  'ru_kpp',
  'sa_vat',
  'sg_gst',
  'sg_uen',
  'si_tin',
  'sv_nit',
  'th_vat',
  'tr_tin',
  'tw_vat',
  'ua_vat',
  'us_ein',
  'uy_ruc',
  've_rif',
  'vn_tin',
  'za_vat',
];

// List of regex and types for non-eu countries with multiple types of taxes ( Canada, Japan,  Russia, Malaysia, Norway, Singapore) and EU non-union
export const EXCEPTION_TAX_REGEX = {
  ca_bn: new RegExp('^\\d{9}$'), // Canadian BN
  ca_gst_hst: new RegExp('^\\d{9}RT\\d{4}$'), // Canadian GST/HST number
  ca_pst_bc: new RegExp('^PST-\\d{4}-\\d{4}$'), // Canadian PST number (British Columbia)
  ca_pst_mb: new RegExp('^\\d{6}-\\d$'), // Canadian PST number (Manitoba)
  ca_pst_sk: new RegExp('^\\d{7}$'), // Canadian PST number (Saskatchewan)
  ca_qst: new RegExp('^\\d{10}TQ\\d{4}$'), // Canadian QST number (Québec)
  jp_cn: new RegExp('^\\d{12,14}$'), // Japanese Corporate Number
  jp_rn: new RegExp('^\\d{5}$'), // Japanese Registered Foreign Businesses' Registration Number
  jp_trn: new RegExp('^[A-Z]\\d{13}$'), // Japanese Tax Registration Number
  my_frp: new RegExp('^\\d{8}$'), // Malaysian FRP number
  my_itn: new RegExp('^[A-Z].{0,12}$'), // Malaysian ITN
  my_sst: new RegExp('^[A-Z]{1}\\d{2}-\\d{4}-\\d{8}$'), // Malaysian SST number
  ru_inn: new RegExp('^\\d{10}$'), // Russian INN
  ru_kpp: new RegExp('^\\d{9}$'), // Russian KPP
  no_vat: new RegExp('^\\d{9}MVA$'), // Norwegian VAT number
  no_voec: new RegExp('^\\d{7}$'), // Norwegian VAT on e-commerce number
  sg_gst: new RegExp('^[A-Z]{1}\\d{7,8}[A-Z]$'), // Singaporean GST
  sg_uen: new RegExp('^\\d{9}[A-Z]{1}$'), // Singaporean UEN
  eu_oss_vat: new RegExp('^EU\\d{9}$'), // European One Stop Shop VAT number for non-Union scheme
};

export const EXCEPTION_COUNTRIES = ['CA', 'JP', 'MY', 'RU', 'NO', 'SG'];
