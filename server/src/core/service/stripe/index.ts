import { ClientException, Code } from "src/core/common";
import { formatAmountForStripe } from "src/core/service/stripe/util";
import { logger } from "src/log";
// import { Subscription } from 'src/routers/types';
import Stripe from "stripe";

import {
  EU_COUNTRIES,
  EXCEPTION_COUNTRIES,
  EXCEPTION_TAX_REGEX,
  STRIPE_SUPPORTED_TYPES,
} from "./constants";

export type StripePaymentOptions = {
  apiKey: string;
};

// TODO: create factory
class StripePayment {
  private client: Stripe;

  constructor(options: StripePaymentOptions) {
    this.client = new Stripe(options.apiKey, {
      apiVersion: "2020-08-27",
    });
  }

  // public getPlan(planId: string): Subscription | null {
  //   switch (planId) {
  //     case process.env.SUB_GROWTH_MONTH:
  //     case process.env.SUB_GROWTH_YEAR: {
  //       return Subscription.Growth;
  //     }
  //     case process.env.SUB_ENTERPRISE_MONTH:
  //     case process.env.SUB_ENTERPRISE_YEAR: {
  //       return Subscription.Enterprise;
  //     }
  //     case process.env.SUB_BUSINESS_MONTH:
  //     case process.env.SUB_BUSINESS_YEAR: {
  //       return Subscription.Business;
  //     }
  //     case process.env.SUB_WHITELABEL: {
  //       return Subscription.Whitelabel;
  //     }
  //     case process.env.SUB_MINI_WHITELABEL: {
  //       return Subscription.MiniWhitelabel;
  //     }
  //     case process.env.SUB_MINI_FULL_WHITELABEL: {
  //       return Subscription.MiniFullWhitelabel;
  //     }
  //     case process.env.SUB_EXPERIENCE: {
  //       return Subscription.Experience;
  //     }
  //     case process.env.SUB_ECOMMERCE: {
  //       return Subscription.Ecommerce;
  //     }
  //     case process.env.SUB_TICKETING: {
  //       return Subscription.Ticketing;
  //     }
  //     case process.env.SUB_AR_MONTH:
  //     case process.env.SUB_AR_YEAR: {
  //       return Subscription.Ar;
  //     }
  //     default: {
  //       return null;
  //     }
  //   }
  // }

  // public getSubscriptionName(planId: string): Subscription | null {
  //   switch (planId) {
  //     case process.env.SUB_GROWTH_MONTH: {
  //       return Subscription.GrowthMonth;
  //     }
  //     case process.env.SUB_GROWTH_YEAR: {
  //       return Subscription.GrowthYear;
  //     }
  //     case process.env.SUB_GROWTH: {
  //       return Subscription.Growth;
  //     }
  //     case process.env.SUB_BUSINESS_MONTH: {
  //       return Subscription.BusinessMonth;
  //     }
  //     case process.env.SUB_BUSINESS_YEAR: {
  //       return Subscription.BusinessYear;
  //     }
  //     case process.env.SUB_BUSINESS: {
  //       return Subscription.Business;
  //     }
  //     case process.env.SUB_ENTERPRISE_MONTH: {
  //       return Subscription.Enterprise;
  //     }
  //     case process.env.SUB_ENTERPRISE_YEAR: {
  //       return Subscription.Enterprise;
  //     }
  //     case process.env.SUB_WHITELABEL: {
  //       return Subscription.Whitelabel;
  //     }
  //     case process.env.SUB_MINI_WHITELABEL: {
  //       return Subscription.MiniWhitelabel;
  //     }
  //     case process.env.SUB_MINI_FULL_WHITELABEL: {
  //       return Subscription.MiniFullWhitelabel;
  //     }
  //     case process.env.SUB_EXPERIENCE: {
  //       return Subscription.Experience;
  //     }
  //     case process.env.SUB_ECOMMERCE: {
  //       return Subscription.Ecommerce;
  //     }
  //     case process.env.SUB_TICKETING: {
  //       return Subscription.Ticketing;
  //     }
  //     case process.env.SUB_AR_MONTH:
  //     case process.env.SUB_AR_YEAR: {
  //       return Subscription.Ar;
  //     }
  //     default: {
  //       return null;
  //     }
  //   }
  // }

  private getTaxType(country: string, value: string) {
    let type: string;

    const isEUCountry = EU_COUNTRIES.includes(country.toUpperCase());
    const isValidEUVat =
      country.toUpperCase() === value.slice(0, 2).toUpperCase();
    const isExceptionCountry = EXCEPTION_COUNTRIES.includes(
      country.toUpperCase()
    ); // exception country means country with few type of taxes

    if (isEUCountry && isValidEUVat) {
      type = "eu_vat";
    } else if (isExceptionCountry) {
      type = Object.keys(EXCEPTION_TAX_REGEX).find((c) => {
        if (
          c.split("_")[0] === country.toLowerCase() &&
          EXCEPTION_TAX_REGEX[c].test(value)
        ) {
          return c;
        }
        return false;
      });
    } else {
      type = STRIPE_SUPPORTED_TYPES.find((c) => {
        if (c.split("_")[0] === country.toLowerCase()) {
          return c;
        }
        return false;
      });
    }

    if (!type && (isEUCountry || isExceptionCountry)) {
      throw ClientException.new({
        overrideMessage:
          "This Tax ID does not meet requirements of payment system. Please contact our support.",
        code: Code.BAD_REQUEST_ERROR,
      });
    }
    if (!type) {
      throw ClientException.new({
        overrideMessage:
          "Payment system does not support Tax ID from your country. Please contact our support.",
        code: Code.BAD_REQUEST_ERROR,
      });
    }
    return type as Stripe.TaxIdCreateParams.Type;
  }

  async pay(parameters: Stripe.PaymentIntentCreateParams): Promise<
    Stripe.PaymentIntent & {
      lastResponse: {
        headers: { [p: string]: string };
        requestId: string;
        statusCode: number;
        apiVersion?: string;
        idempotencyKey?: string;
        stripeAccount?: string;
      };
    }
  > {
    return this.client.paymentIntents.create({
      ...parameters,
      amount: formatAmountForStripe(parameters.amount, parameters.currency),
    });
  }

  async createCustomer(parameters: Stripe.CustomerCreateParams) {
    const customer = await this.client.customers.create(parameters);
    return customer?.id;
  }

  async updateCustomer(id: string, parameters: Stripe.CustomerUpdateParams) {
    const customer = await this.client.customers.update(id, parameters);
    return customer?.id;
  }

  async updateTax(id: string, country: string, value: string) {
    if (!country || !id || !value) return;
    const type = this.getTaxType(country, value);
    const taxes = await this.client.customers.listTaxIds(id);
    await Promise.all(
      taxes.data.map((tax: Stripe.TaxId) => {
        return this.client.customers.deleteTaxId(id, tax.id);
      })
    );
    await this.client.customers
      .createTaxId(id, { type, value })
      .catch((error) => {
        logger.error(
          `UserController>>edit>>updateTax>> Message: ${error.message}`
        );
        if (error.raw?.code === "tax_id_invalid") {
          throw ClientException.new({
            overrideMessage:
              "This Tax ID does not meet requirements of payment system. Please contact our support.",
            code: Code.BAD_REQUEST_ERROR,
          });
        }
        throw new Error("Error updating tax id");
      });
  }

  async getInvoices(
    customer: string,
    start: number,
    end: number,
    limit: number = 10,
    startingAfter?: string
  ) {
    const invoices = await this.client.invoices.list({
      customer,
      created: {
        gte: start,
        lte: end,
      },
      limit,
      starting_after: startingAfter,
    });
    return invoices;
  }

  async getInvoice(id: string) {
    const invoice = await this.client.invoices.retrieve(id);
    return invoice;
  }

  async getCustomer(id: string) {
    return this.client.customers.retrieve(id);
  }

  async getPrice(id: string) {
    return this.client.prices.retrieve(id);
  }

  async createIntent(customerId: string) {
    const intent = await this.client.setupIntents.create({
      customer: customerId,
      payment_method_types: ["card"],
    });
    return intent?.client_secret;
  }

  private async getPaymentMethods(customerId: string) {
    return this.client.paymentMethods.list({
      customer: customerId,
      type: "card",
    });
  }

  private generateCouponCode() {
    const length = 6;
    let result = "";
    const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    const charactersLength = characters.length;
    for (let index = 0; index < length; index++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
  }

  async createDiscount(name: string, off: number, product: string) {
    const code = this.generateCouponCode();
    await this.client.coupons.create({
      percent_off: off,
      duration: "once",
      name: name,
      id: code,
      max_redemptions: 1,
      applies_to: {
        products: [product],
      },
    });
    return code;
  }

  async getCard(customerId: string) {
    const list = await this.getPaymentMethods(customerId);
    return list?.data?.length > 0 ? list.data[0] : null; //we support only 1 credit card per customer
  }

  async deleteCard(customerId: string) {
    const card = await this.getCard(customerId);
    return this.client.paymentMethods.detach(card?.id);
  }

  async createSubscription(parameters: Stripe.SubscriptionCreateParams) {
    return this.client.subscriptions.create(parameters);
  }

  async updateSubscription(
    id: string,
    parameters: Stripe.SubscriptionUpdateParams
  ) {
    return this.client.subscriptions.update(id, parameters);
  }

  async deleteSubscription(id: string) {
    return this.client.subscriptions.del(id);
  }

  async getSubscription(id: string) {
    return this.client.subscriptions.retrieve(id);
  }

  async getProduct(id: string) {
    return this.client.products.retrieve(id);
  }

  async getSubscriptions(customer: string) {
    return this.client.subscriptions.list({ customer });
  }

  async listSubscriptions(customerId: string) {
    return this.client.subscriptions.list({ customer: customerId });
  }

  async getCoupon(id: string) {
    return this.client.coupons.retrieve(id);
  }
}

export default StripePayment;
