import { Server as HTTPServer } from 'node:http';

import { get } from 'lodash';
import { Server, ServerOptions, Socket } from 'socket.io';
import { ExtendedError } from 'socket.io/dist/namespace';
import { logger } from 'src/log';

import { ClassType } from '../../common';

let _io;

type SocketMiddleware = (
  socket: Socket,
  next: (error?: ExtendedError) => void,
) => void;

export class SocketService {
  constructor(server: HTTPServer, options?: Partial<ServerOptions>) {
    logger.debug('Creating socket...');
    _io = new Server(server, options);
  }

  public static getIO() {
    return _io;
  }

  public attachControllers(controllers) {
    for (const Ctrl of controllers) new Ctrl();
  }
}

export function withSocket(
  namespace: string,
  ...arguments_: SocketMiddleware[]
) {
  // applying sockets middlewares
  for (const m of arguments_) _io.use(m);

  // eslint-disable-next-line @typescript-eslint/ban-types
  return function <T extends ClassType<{}>>(Controller: T) {
    // take all class methods as events
    const ioEvents: string[] = Object.getOwnPropertyNames(
      Controller.prototype,
    ).filter((property) => property !== 'constructor');

    return class extends Controller {
      io = _io;

      constructor(...properties: any[]) {
        super(...properties);
        logger.debug(`${namespace} socket is ready to listen`);
        this.io.of(namespace).on('connection', (socket: Socket) => {
          for (let index = 0; index < ioEvents.length; index++) {
            const event = get(ioEvents, index);
            // calling each event method to start listening
            // socket should be unique for each connection
            get(Controller.prototype, event as any).call(this, socket);
          }
        });
      }
    };
  };
}

export function OnEvent(event: string) {
  return function (target: any, key: string, descriptor: PropertyDescriptor) {
    const original = descriptor.value;
    descriptor.value = async function (socket: Socket) {
      // method
      // @ts-ignore
      socket.on(event, async (data: any) => {
        try {
          await original.call(this, data, socket);
        } catch (error) {
          logger.error(`socket:'${event}': ${error.message}`);
          // TODO: implement DTO
          socket.emit('socket_error', {
            message: error.message,
            event,
          });
        }
      });
    };

    return descriptor;
  };
}
