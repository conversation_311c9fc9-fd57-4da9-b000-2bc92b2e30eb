import { plainToClass } from 'class-transformer';
import { validate as validator, ValidationError } from 'class-validator';
import { NextFunction, Request, Response } from 'express';

import { CoreApiResponse } from '../api';
import { Code } from '../code/Code';
import { RequestType } from '../type';

export enum REQUEST_TYPE {
  BODY = 'body',
  PARAMS = 'params',
  QUERY = 'query',
}

export default function validate(
  ValidationClass: any,
  options: { type: REQUEST_TYPE } = { type: REQUEST_TYPE.BODY },
): any {
  return function (
    target: object,
    key: string | symbol,
    descriptor: PropertyDescriptor,
  ) {
    const { type } = options;
    const original = descriptor.value;

    descriptor.value = async function (
      request: RequestType<Request>,
      res: Response,
      next: NextFunction,
    ) {
      try {
        const data = request[type];
        const dtoObject = plainToClass(ValidationClass, data);

        const errors: ValidationError[] = await validator(dtoObject);
        if (errors.length > 0) {
          const dtoErrors = Object.fromEntries(
            errors.map((error: ValidationError) => [
              error.property,
              (Object as any).values(error.constraints),
            ]),
          );

          return res
            .status(Code.VALIDATION_ERROR.code)
            .json(
              CoreApiResponse.error(
                Code.VALIDATION_ERROR.code,
                Code.VALIDATION_ERROR.message,
                dtoErrors,
              ),
            );
        }

        request[type] = plainToClass(ValidationClass, data, {
          excludeExtraneousValues: true,
          enableCircularCheck: true,
        });

        return await Reflect.apply(original, this, [request, res, next]);
      } catch (error) {
        if (error.status !== Code.VALIDATION_ERROR.code) {
          throw error;
        }
        return res
          .status(Code.VALIDATION_ERROR.code)
          .json(
            CoreApiResponse.error(
              Code.BAD_REQUEST_ERROR.code,
              'Validation error',
            ),
          );
      }
    };

    return descriptor;
  };
}
