const assert = require('node:assert').strict;
import * as util from 'node:util';

import AssertionError from '../exception/AssertionException';

const validTypes = [Number, String, Object, Array, Boolean, Function];

function isObject(v) {
  return v && typeof v === 'object' && !Array.isArray(v);
}

export const Assert = {
  fail(actual, expected, message = '') {
    throw new AssertionError(
      message ||
        `Failed value: ${util.inspect(actual)}; ${
          expected !== undefined
            ? `Expect: ${util.inspect(expected.name || expected)}`
            : ''
        }`,
    );
  },

  ok(value, { message = '', required = false } = {}) {
    if (!value && required) Assert.fail(value, 'Truthful value', message);
    if (value !== undefined && !value)
      Assert.fail(value, 'Truthful value', message);
  },

  typeOf(value, type, message) {
    if (!validTypes.includes(type)) {
      Assert.fail(
        value,
        type,
        message ||
          `Assert.typeOf accept one of [${validTypes.map(
            (t) => t.name,
          )}] types. Use another method to validate "${type}"`,
      );
    }

    if (type === Number && typeof value === 'number' && !Number.isNaN(value))
      return;
    if (type === String && typeof value === 'string') return;
    if (type === Object && isObject(value)) return;
    if (type === Array && Array.isArray(value)) return;
    if (type === Boolean && typeof value === 'boolean') return;
    if (type === Function && typeof value === 'function') return;

    Assert.fail(value, type, message);
  },

  object(value, { required = false, notEmpty = false, message = '' } = {}) {
    if (required || notEmpty) Assert.typeOf(value, Object, message);
    if (value !== undefined) Assert.typeOf(value, Object, message);
    if (notEmpty && Object.keys(value).length === 0)
      Assert.fail(value, 'Not empty object', message);
  },

  equal(actual, expected, message = '') {
    assert.strictEqual(
      actual,
      expected,
      new AssertionError(message || 'Values is not equal'),
    );
  },
};
