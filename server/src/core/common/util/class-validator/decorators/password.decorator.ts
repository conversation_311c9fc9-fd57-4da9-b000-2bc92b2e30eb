import { registerDecorator, ValidationOptions } from 'class-validator';

import { password } from '../../password-validation';

export function IsPasswordValid(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      constraints: [],
      options: validationOptions,
      validator: {
        validate(value: any) {
          const [error, isValid] = password.isStrong(value);
          if (error) this.error = error;
          return Boolean(isValid);
        },
        defaultMessage(): string {
          return this.error || 'Something went wrong';
        },
      },
    });
  };
}
