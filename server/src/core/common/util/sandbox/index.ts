import * as fs from 'node:fs';
import * as path from 'node:path';
import * as vm from 'node:vm';

const EMPTY_CONTEXT = vm.createContext(Object.freeze({}));

const RUN_OPTIONS = { timeout: 5000, displayErrors: false };
const CONTEXT_OPTIONS = { microtaskMode: 'afterEvaluate' };
const USE_STRICT = `'use strict';\n`;

const COMMON_CONTEXT = vm.createContext(
  Object.freeze({
    setTimeout,
    setImmediate,
    setInterval,
    clearTimeout,
    clearInterval,
    require,
    fs,
  }),
);

const createContext = (context = {}, preventEscape = false) => {
  if (!context) return EMPTY_CONTEXT;
  // @ts-ignore
  return vm.createContext(context, preventEscape ? CONTEXT_OPTIONS : {});
};

const readScript = async (filePath, options) => {
  const source = await fs.promises.readFile(filePath, 'utf8');
  if (!source) return null;
  const name = options?.filename
    ? options.filename
    : path.basename(filePath, '.js');
  const strict = source.startsWith(USE_STRICT);
  const code = strict ? source : USE_STRICT + source;
  const lineOffset = strict ? 0 : -1;

  const scriptOptions = { filename: name, ...options, lineOffset };
  const script = new vm.Script(code, scriptOptions);
  const context = options.context || createContext(scriptOptions);
  return script.runInContext(context, RUN_OPTIONS);
};

export { COMMON_CONTEXT, createContext, EMPTY_CONTEXT, readScript };
