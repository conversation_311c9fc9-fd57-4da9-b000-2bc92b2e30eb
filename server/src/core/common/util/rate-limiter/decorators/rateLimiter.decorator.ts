import { ClientException, Code } from 'src/core/common';

import limiter from '../rateLimiter';

export default function rateLimiter(
  name: string,
  index: number = 0,
  options: {
    maxFail: number;
    duration: number;
    blockDuration: number;
  } = {
    maxFail: 10,
    duration: 30,
    blockDuration: 15,
  },
): any {
  const points = options.maxFail;
  const limiterFail = limiter(name, {
    ...options,
    points,
  });

  return function (
    target: object,
    key: string | symbol,
    descriptor: PropertyDescriptor,
  ) {
    const original = descriptor.value;
    descriptor.value = async function (...arguments_: any[]) {
      try {
        const rlResEmail = await limiterFail.get(arguments_[index]);
        if (rlResEmail !== null && rlResEmail.consumedPoints > points) {
          throw ClientException.new({
            overrideMessage: 'Too many attempts',
            code: Code.TOO_MANY_REQUESTS,
          });
        }

        const res = await original.apply(this, arguments_);
        await limiterFail.delete(arguments_[index]);
        return res;
      } catch (error) {
        await limiterFail.consume(arguments_[index]);
        if (error instanceof ClientException) throw error;
        throw ClientException.new({
          overrideMessage: error.message,
          code: Code.BAD_REQUEST_ERROR,
        });
      }
    };

    return descriptor;
  };
}
