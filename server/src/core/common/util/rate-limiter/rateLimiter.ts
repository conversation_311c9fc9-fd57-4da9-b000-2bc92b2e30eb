import { RateLimiterRedis } from 'rate-limiter-flexible';
import * as redis from 'redis';
import { HOUR, MINUTES } from 'src/constants';

const redisClient = redis.createClient({
  url: process.env.REDISCLOUD_URL,
});

const rateLimiter = (
  keyPrefix,
  { points, duration, blockDuration } = {
    points: 10,
    duration: HOUR,
    blockDuration: 15 * MINUTES,
  },
) => {
  return new RateLimiterRedis({
    storeClient: redisClient,
    keyPrefix,
    points,
    duration,
    blockDuration,
  });
};
export default rateLimiter;
