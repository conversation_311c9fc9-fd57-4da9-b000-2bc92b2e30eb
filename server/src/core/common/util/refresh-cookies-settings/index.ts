import { CookieOptions } from "express";
import config from "src/config";

export const REFRESH_TOKEN = "refreshToken";

export function parseTimeToMilliseconds(timeString: string): number {
  const value = Number.parseInt(timeString);
  const unit = timeString.slice(-1);

  switch (unit) {
    case "d": {
      return value * 24 * 60 * 60 * 1000;
    } // days to ms
    case "h": {
      return value * 60 * 60 * 1000;
    } // hours to ms
    case "m": {
      return value * 60 * 1000;
    } // minutes to ms
    default: {
      return value * 60 * 1000;
    } // default to minutes
  }
}

export function getRefreshTokenCookieSettings(): CookieOptions {
  const refreshTime = config.get("jwt.refreshExpireTime"); // can be "30d", "7h", "60m" or "43200"
  const maxAge = parseTimeToMilliseconds(refreshTime);

  return {
    httpOnly: true,
    secure: true,
    sameSite: "none",
    maxAge,
  };
}
