import { format } from '@fast-csv/format';

const exportCsv = ({ name, headers, data, res }) => {
  const csvStream = format({ headers });
  csvStream.pipe(res).on('end', () => {
    res.header({
      'Content-Type': 'text/csv;base64',
      'Content-Disposition': `attachment; filename=${name}.csv`,
    });
    res.end();
  });

  data.forEach((order: any) => {
    csvStream.write(order);
  });

  csvStream.end();
};

export default exportCsv;
