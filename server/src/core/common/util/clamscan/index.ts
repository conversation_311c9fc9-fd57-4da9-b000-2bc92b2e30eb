const { workerData, parentPort } = require('node:worker_threads');
const NodeClam = require('clamscan');
import config from '../../../../config';
import { logger } from '../../../../log';

async function scan(data) {
  let response = true;
  try {
    const ClamScan = await new NodeClam().init({
      remove_infected: true,
      preference: config.get('upload.virusScan.type'),
      clamdscan: {
        host: config.get('upload.virusScan.host'),
        port: config.get('upload.virusScan.port'),
      },
    });
    logger.info('Start scan');
    for await (const file of data?.files || []) {
      logger.info('File scanning ' + file);
      const { is_infected: isInfected, viruses } = await ClamScan.is_infected(
        file,
      );
      if (isInfected) {
        logger.info(`${file} is infected with ${viruses.join(', ')}.`);
        return isInfected;
      } else {
        logger.info(`${file} not infected.`);
      }
    }
    logger.info('End scan');
    response = false;
  } catch (error) {
    logger.error('Error virus scanner', error.message);
  }
  parentPort.postMessage(response);
}

scan(workerData).catch((error) => {
  logger.error('Error virus scanner', error.message);
});
