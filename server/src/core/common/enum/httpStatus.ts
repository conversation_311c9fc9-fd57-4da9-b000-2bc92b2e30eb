export enum Status {
  OK = 'OK',
  INVALID_REQUEST = 'Invalid request',
  MAX_WAYPOINTS_EXCEEDED = 'Invalid request',
  MAX_ROUTE_LENGTH_EXCEEDED = 'Invalid request',
  OVER_DAILY_LIMIT = 'Invalid request',
  OVER_QUERY_LIMIT = 'Invalid request',
  REQUEST_DENIED = 'Invalid request',
  UNKNOWN_ERROR = 'Invalid request. Please try again.',
  ZERO_RESULTS = 'Invalid address',
  NOT_FOUND = 'Address not found',
}
