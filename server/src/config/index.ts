import * as path from 'node:path';

import * as dotenv from 'dotenv';
import { getAppConfig } from 'src/config/app.config';
import { getSSOConfig } from 'src/config/sso.config';
import { Config } from 'src/core/service/configService';

dotenv.config({ path: path.join(__dirname, '..', '..', '.env') });

export const getConfigVariables = () => {
  const generalConfig: Config = getAppConfig();
  return {
    ...generalConfig,
    sso: getSSOConfig({
      apiDomain: generalConfig.general.apiDomain,
    }),
  };
};

export { default } from 'src/core/service/configService';
