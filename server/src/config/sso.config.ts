import { get } from 'env-var';
import { normalizeBoolean } from 'src/helpers/utils';

const IS_ENABLED_SSO = normalizeBoolean(get('IS_ENABLED_SSO').asString());

const getSSOConfig = ({ apiDomain }) => ({
  sp: {
    entityId: (domain: string) => `https://${domain}/admin/login`,
    assertEndpoint: (domain: string, providerCode: string) =>
      `${apiDomain}/v1/public/sso/${providerCode}/assert?domain=${domain}`,
    allowUnencryptedAssertion: true,
  },
  isEnabledSSO: IS_ENABLED_SSO,
});

export { getSSOConfig };
