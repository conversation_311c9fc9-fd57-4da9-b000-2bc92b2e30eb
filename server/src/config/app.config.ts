import * as path from "node:path";

import { get } from "env-var";

const getAppConfig = () => ({
  general: {
    appDomain: get("APP_DOMAIN").default("https://gridevo.com").asString(),
    apiDomain: get("API_DOMAIN").required().asString(),
    env: get("NODE_ENV").default("development").asString(),
  },

  media: {
    url: get("MEDIA_HOST_URL").required().asString(),
  },

  zendesk: {
    email: get("ZENDESK_EMAIL").required().asString(),
    token: get("ZENDESK_API_KEY").required().asString(),
  },

  mongo: {
    enabled: get("MONGO_ENABLED").default("false").asBool(),
  },
  stripe: {
    apiKey: get("STRIPE_APIKEY").required().asString(),
    subscription: {
      mini_whitelabel: get("SUB_MINI_WHITELABEL").asString(),
      mini_full_whitelabel: get("SUB_MINI_FULL_WHITELABEL").asString(),
      whitelabel: get("SUB_WHITELABEL").asString(),
      ecommerce: get("SUB_ECOMMERCE").asString(),
      experience: get("SUB_EXPERIENCE").asString(),
      ticketing: get("SUB_TICKETING").asString(),
      business_month: get("SUB_BUSINESS_MONTH").asString(),
      business_year: get("SUB_BUSINESS_YEAR").asString(),
      growth_month: get("SUB_GROWTH_MONTH").asString(),
      growth_year: get("SUB_GROWTH_YEAR").asString(),
      enterprise: get("SUB_ENTERPRISE_YEAR").asString(),
      ar_month: get("SUB_AR_MONTH").asString(),
      ar_year: get("SUB_AR_YEAR").asString(),
    },
  },

  mail: {
    postMarkApiKey: get("POSTMARK_API_KEY").required().asString(),
    from: get("GRIDEVO_EMAIL").required().asString(),
  },

  port: get("PORT").default(5030).asIntPositive(),

  path: {
    root: path.join(__dirname, "..", ".."),
  },

  api: {
    url: get("API_DOMAIN").default("https://api.gridevo.com").asString(),
    uiAuth: {
      login: get("UI_API_LOGIN").required().asString(),
      password: get("UI_API_PASSWORD").required().asString(),
    },
  },

  eCommerce: {
    currency: "USD",
  },

  twilio: {
    defaultNumber: get("TWILIO_DEFAULT_NUMBER").required().asString(),
  },

  aws: {
    accessKeyId: get("AWS_KEY_ID").required().asString(),
    secretAccessKey: get("AWS_SECRET_KEY").required().asString(),
    region: get("AWS_REGION").default("us-east-1").required().asString(),
    ssm: {
      path: `/server/${get("AWS_ENV").default("dev").required().asString()}`,
      accessKeyId: get("SSM_AWS_KEY_ID").asString(),
      secretAccessKey: get("SSM_AWS_SECRET_KEY").asString(),
    },
    bucket: get("AWS_BUCKET").asString(),
  },

  upload: {
    folder: "uploads",
    virusScan: {
      enabled: get("IS_ENABLE_VIRUS_SCANNER").required().asBool(),
      type: get("VIRUS_SCANNER_TYPE").default("clamdscan").asString(),
      host: get("VIRUS_SCANNER_HOST").default("localhost").asString(),
      port: get("VIRUS_SCANNER_PORT").default(3310).asIntPositive(),
    },
  },

  captcha: {
    enabled: get("RECAPTCHA_IS_ENABLED").required().asBool(),
    secret: get("RECAPTCHA_SECRET").required().asString(),
  },

  jwt: {
    // expireTime: "30d",
    // secret: get("TOKEN").asString(),
    accessSecret: get("ACCESS_SECRET").default("access-secret").asString(),
    accessExpireTime: get("ACCESS_EXPIRATION_TIME").default("30s").asString(),
    refreshSecret: get("REFRESH_SECRET").default("refresh-secret").asString(),
    refreshExpireTime: get("REFRESH_EXPIRATION_TIME").default("7d").asString(),
  },

  google: {
    propertyId: get("GA4_PROPERTY_ID").required().asString(),
    mobileStreamId: get("GA4_MOBILE_STREAM_ID").required().asString(),
  },

  redis: {
    url: process.env.REDISCLOUD_URL,
  },
  system: {
    slack: {
      token: get("SLACK_BOT_TOKEN").default("").asString(),
    },
  },

  convertE57: {
    jobDefinitionName: "converter-e57-job",
    jobQueueByName: "convert-e57-job-queue",
    bucketName: get("IMMERSEL_AWS_BUCKET").asString(),
  },

  immerToken: get("IMMERSAL_TOKEN").default("").asString(),
});

export { getAppConfig };
