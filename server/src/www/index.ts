import "source-map-support/register";
import "reflect-metadata";

import * as dotenv from "dotenv";
dotenv.config();
process.env.TZ = "UTC"; // Ensure all operations use UTC

import config, { getConfigVariables } from "src/config";
import { logger } from "src/log";

import { App } from "../app";

process.on("unhandledRejection", (reason: any, promise) => {
  // Log the unhandled rejection in a structured format
  logger.error("Unhandled Rejection at:", {
    reason: reason.message || reason.toString(),
    promise,
    stack: reason.stack || "No stack available",
  });

  // Optionally, you can exit the process if required
  // process.exit(1);  // Exit the process on unhandled rejection (optional)
});

process.on("uncaughtException", (error: Error) => {
  // Log the uncaught exception in a structured format
  logger.error("Uncaught Exception:", {
    message: error.message,
    stack: error.stack,
    name: error.name,
  });

  // Exit the process (or restart if using a process manager like PM2)
  process.exit(1);
});

process.on("SIGTERM", () => {
  logger.error(`Process ${process.pid} received a SIGTERM signal`);

  // Implement graceful shutdown logic here
  shutdownGracefully(() => {
    process.exit(0); // Exit after shutting down gracefully
  });
});

process.on("SIGINT", () => {
  logger.error(`Process ${process.pid} has been interrupted (SIGINT)`);

  // Implement graceful shutdown logic here
  shutdownGracefully(() => {
    process.exit(0); // Exit after shutting down gracefully
  });
});

process.on("exit", (code) => {
  logger.info(`Process exited with code: ${code}`);
});

const shutdownGracefully = (callback: () => void) => {
  logger.info("Shutting down gracefully...");

  // Add your shutdown logic here (e.g., closing DB connections, stopping HTTP servers, etc.)

  // Simulating async tasks during shutdown
  setTimeout(() => {
    logger.info("Completed graceful shutdown.");
    callback();
  }, 1000); // Delay for simulation, adjust based on your tasks
};

const port = normalizePort(process.env.PORT || 5030);
let server;

const initializeServer = async () => {
  try {
    if (process.env.AWS_ENV) {
      await config.getConfig();
    }
    config.getInstance(getConfigVariables());

    server = new App(port).server;

    server.listen(port);
    server.on("error", onError);
    server.on("listening", onListening);
  } catch (error) {
    logger.error("Failed to retrieve parameters:", error);
    process.exit(1);
  }
};

initializeServer();

function normalizePort(value: number | string): number {
  const normalizedPort: number =
    typeof value === "string" ? Number.parseInt(value, 10) : value;
  if (!Number.isNaN(normalizedPort) && normalizedPort >= 0)
    return normalizedPort;
  throw new Error("Invalid port");
}

function onError(error: NodeJS.ErrnoException): void {
  if (error.syscall !== "listen") {
    logger.error(`Error: ${error.message}`);
    throw error;
  }

  const bind = typeof port === "string" ? "Pipe " + port : "Port " + port;
  switch (error.code) {
    case "EACCES": {
      logger.error(`${bind} requires elevated privileges`);
      process.exit(1);
      break;
    }
    case "EADDRINUSE": {
      logger.error(`${bind} is already in use`);
      process.exit(1);
      break;
    }
    default: {
      logger.error(`Error: ${error.message}`);
      throw error;
    }
  }
}

function onListening(): void {
  const addr = server.address();
  const bind = typeof addr === "string" ? `pipe ${addr}` : `port ${addr.port}`;
  logger.info(`Listening on ${bind}`);
}
