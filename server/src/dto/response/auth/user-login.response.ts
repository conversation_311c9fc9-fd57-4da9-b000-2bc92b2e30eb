import 'reflect-metadata';

import { Expose, plainToClass, Type } from 'class-transformer';
import { IsString } from 'class-validator';

import { UserResponse } from '../user';

export class UserLoginResponse {
  @Expose()
  @IsString()
  token: string;

  @Type(() => UserResponse)
  @Expose()
  user: UserResponse;

  public static mapFrom<P>(data: Required<P>): UserLoginResponse {
    return plainToClass(UserLoginResponse, data, {
      excludeExtraneousValues: true,
    });
  }
}
