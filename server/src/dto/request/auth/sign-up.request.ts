import { Expose } from "class-transformer";
import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  Max<PERSON>ength,
  <PERSON><PERSON>ength,
} from "class-validator";

import { Match } from "../../../core/common/util/class-validator/decorators";

export class SignUpRequest {
  @IsString()
  @Expose()
  name: string;

  @IsString()
  @MinLength(4)
  @MaxLength(50)
  @IsNotEmpty()
  @IsEmail()
  @Expose()
  email: string;

  @IsString()
  @MaxLength(50)
  @IsOptional()
  @Expose()
  company: string;

  @IsString()
  @MinLength(2)
  @MaxLength(50)
  @IsOptional()
  @Expose()
  country: string;

  @IsString()
  @MinLength(4)
  @MaxLength(50)
  @IsOptional()
  @Expose()
  phone: string;

  @IsString()
  @IsOptional()
  @Expose()
  refer: string;

  @IsString()
  @Expose()
  password: string;

  @IsString()
  @Match("password")
  @Expose()
  validatePassword: string;

  @IsString()
  @IsOptional()
  @Expose()
  captchaToken: string;

  @IsString()
  @IsOptional()
  @Expose()
  googleId: string;
}
