import { Expose } from 'class-transformer';
import {
  IsEmail,
  IsNotEmpty,
  IsNumber,
  IsPositive,
  IsString,
  MaxL<PERSON>th,
  Min<PERSON>eng<PERSON>,
} from 'class-validator';

export class UpdateEmailRequest {
  @IsNumber()
  @IsPositive()
  @Expose()
  userId: number;

  @IsString()
  @MinLength(4)
  @MaxLength(50)
  @IsNotEmpty()
  @IsEmail()
  @Expose()
  newEmail: string;

  @IsString()
  @IsNotEmpty()
  @Expose()
  captchaToken: string;
}
