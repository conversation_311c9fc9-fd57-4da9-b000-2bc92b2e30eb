import config from "src/config";
import { logger } from "src/log";
import {
  ClientException,
  Code,
  getRefreshTokenCookieSettings,
  REFRESH_TOKEN,
  UserType,
} from "../core/common";
import { createAuthTokens } from "src/helpers/utils";
import { Request, Response, NextFunction } from "express";

import db from "src/db/models";
import * as jose from "jose";
const { base64url, jwtVerify } = jose;
const lockMap = new Map<number, Promise<string>>();

type JWTUserDecode = {
  userId: number;
  userType: UserType;
};

interface IDeviceInfo {
  fingerprint?: string;
  userAgent?: string;
  language?: string;
  [key: string]: any;
}

interface TokenVerificationResult {
  decoded: JWTUserDecode;
}

const AUTHORIZATION_HEADER = "Authorization";
const HOST_HEADER = "host";

export const refreshAccessToken = async (
  decoded: JWTUserDecode,
  refreshToken: string
): Promise<string> => {
  const userId = decoded.userId;

  if (lockMap.has(userId)) {
    const result = await lockMap.get(userId);
    return result;
  }

  const refreshPromise = (async () => {
    try {
      const result = await db.sequelize.transaction(async (transaction) => {
        // const idField = getSessionIdField(decoded.userType);
        // const session = await new DbAuthSession().get({
        //   query: {
        //     [idField]: decoded.userId,
        //     refreshToken,
        //   },
        //   transaction,
        //   lock: transaction.LOCK.UPDATE,
        //   disableCatchError: true,
        // });

        // if (!session) {
        //   throw ClientException.new({
        //     code: Code.UNAUTHORIZED_ERROR,
        //   });
        // }

        // if (session.accessToken && session.accessTokenExpiresAt > new Date()) {
        //   return session.accessToken;
        // }

        const { accessToken: newAccessToken } = await createAuthTokens(
          decoded.userId,
          decoded.userType
        );

        const accessTokenExpiresAt = new Date();
        accessTokenExpiresAt.setMinutes(
          accessTokenExpiresAt.getMinutes() +
            Number.parseInt(config.get("jwt.accessExpireTime"))
        );

        return newAccessToken;
      });

      return result;
    } catch (error) {
      throw error;
    } finally {
      lockMap.delete(userId);
    }
  })();

  lockMap.set(userId, refreshPromise);
  return refreshPromise;
};

const verifyAuthToken = async (
  token: string,
  req: Request,
  res: Response
): Promise<TokenVerificationResult> => {
  const JWT_SECRET = config.get("jwt.accessSecret");
  const refreshToken = req.cookies[REFRESH_TOKEN];
  const deviceInfo: IDeviceInfo = {
    userAgent: req.headers["user-agent"],
    fingerprint: req.headers["x-device-fingerprint"] as string,
  };

  try {
    // Verify access token with jose
    const { payload } = await jwtVerify(token, base64url.decode(JWT_SECRET));

    // Standard token validation
    return {
      decoded: {
        userId: payload.userId as number,
        userType: payload.userType as UserType,
      },
    };
  } catch {
    if (!refreshToken) {
      throw ClientException.new({
        code: Code.UNAUTHORIZED_ERROR,
        overrideMessage: "Your session has expired. Please login again.",
      });
    }

    // Refresh token flow
    try {
      const { payload } = await jwtVerify(
        refreshToken,
        base64url.decode(config.get("jwt.refreshSecret"))
      );

      const decoded = {
        userId: payload.userId as number,
        userType: payload.userType as UserType,
      };

      // Validate refresh token against DB
      // const idField = getSessionIdField(decoded.userType);
      // const session = await new DbAuthSession().get({
      //   query: {
      //     [idField]: decoded.userId,
      //     refreshToken,
      //     device: {
      //       [db.Sequelize.Op.contains]: { fingerprint: deviceInfo.fingerprint },
      //     },
      //   },
      //   disableCatchError: true,
      // });

      // if (!session) {
      //   await deleteExpiredSession(refreshToken, deviceInfo);
      //   throw ClientException.new({ code: Code.UNAUTHORIZED_ERROR });
      // }

      // Issue new access token
      const newAccessToken = await refreshAccessToken(decoded, refreshToken);
      res.setHeader("x-access-token", newAccessToken);

      return decoded;
    } catch {
      // await deleteExpiredSession(refreshToken, deviceInfo);
      throw ClientException.new({
        code: Code.UNAUTHORIZED_ERROR,
        overrideMessage: "Your session has expired. Please login again.",
      });
    }
  }
};

export async function checkRole(role: UserType, decoded, headers: any[] = []) {
  if (!decoded.userId) {
    throw ClientException.new({
      code: Code.UNAUTHORIZED_ERROR,
    });
  }

  switch (role) {
    case UserType.SuperAdmin:
    case UserType.Admin:
    case UserType.Manager: {
      if (decoded.userType !== role) {
        return null;
      }
      // authorized as admin

      /** replacing the admins id to a users id
       * allows admin to edit tours as a user */
      const id = decoded.userId;

      return {
        type: decoded.userType,
        id: id,
        selfId: decoded.userId,
      };
    }

    case UserType.Employee: {
      if (decoded.userType !== UserType.Employee) {
        return null;
      }

      return {
        type: decoded.userType,
        id: decoded.userId,
        name: decoded.name,
        email: decoded.email,
      };
    }
    default: {
      throw ClientException.new({
        code: Code.UNAUTHORIZED_ERROR,
      });
    }
  }
}

const getUrl = (request) =>
  `${request.protocol}://${request.get(HOST_HEADER)}${request.originalUrl}`;

export default function authorize(roles: Array<UserType>) {
  return function (
    target: object,
    key: string | symbol,
    descriptor: PropertyDescriptor
  ) {
    const original = descriptor.value;

    descriptor.value = async function (
      request: Request,
      res: Response,
      next: NextFunction
    ) {
      try {
        if (request.method !== "OPTIONS") {
          const authHeader = request.get(AUTHORIZATION_HEADER);
          // let token;

          if (!authHeader) {
            throw ClientException.new({
              code: Code.UNAUTHORIZED_ERROR,
            });
          }

          const token = authHeader.startsWith("Bearer ")
            ? authHeader.split(" ")[1]
            : authHeader;

          const { decoded } = await verifyAuthToken(token, request, res);

          let account: any;
          for await (const role of roles) {
            const result = await checkRole(
              role,
              decoded,
              request.headers as any
            );
            if (result) {
              account = result;
              break;
            }
          }

          if (!account) {
            logger.info(
              `User (id: ${decoded.userId}, role: ${
                decoded.userType
              }) has not access to ${getUrl(request)}`
            );
            throw ClientException.new({
              code: Code.ACCESS_DENIED_ERROR,
            });
          }

          request.account = account;
        }

        const result = await Reflect.apply(original, this, [
          request,
          res,
          next,
        ]);
        return result;
      } catch (error) {
        if (error instanceof ClientException) {
          return res.status(error.status).json({
            message: error.message,
          });
        }
        return res.status(error.status || Code.BAD_REQUEST_ERROR.code).json({
          message: Code.BAD_REQUEST_ERROR.message,
        });
      }
    };

    return descriptor;
  };
}
