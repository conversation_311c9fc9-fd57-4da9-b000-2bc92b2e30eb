import * as _ from "lodash";
import config from "src/config";
import {
  ClientException,
  Code,
  parseTimeToMilliseconds,
  UserType,
} from "src/core/common";

import { ONE_SECOND } from "./date";
import { TokenInfoType } from "src/core/common/type/TokenInfoType";

import * as jose from "jose";
const { base64url, SignJWT } = jose;

export const cloneDeep = _.cloneDeep;

export const groupBy = _.groupBy;

export const isNil = (x): boolean => x == undefined;

export const isEmpty = (x): boolean =>
  isNil(x) || x === "undefined" || x === "null";

export const mapDataToModel = async <T>(Model, data: any) => {
  const model = cloneDeep(Model);
  for (const key of Object.keys(data as T)) {
    if (!isNil(data[key])) {
      model[key] = data[key];
      if (model[key] == "null" || model[key]?.length == 0) model[key] = null;
    }
  }
  return model;
};

export const filterUndefinedData = <T>(data: any) => {
  const newObject: any = {};
  for (const key of Object.keys(data as T)) {
    if (data[key] !== "undefined" && data[key] !== undefined) {
      if (Array.isArray(data[key])) {
        newObject[key] = data[key].map((value) => {
          return typeof value === "object" ? filterUndefinedData(value) : value;
        });
      } else if (data[key] === null) {
        newObject[key] = data[key];
      } else if (typeof data[key] === "object") {
        newObject[key] = filterUndefinedData(data[key]);
      } else {
        newObject[key] = data[key];
      }
    }
  }
  return newObject;
};

export const filterEmptyData = <T>(data: any) => {
  const newObject: any = {};
  for (const key of Object.keys(data as T)) {
    if (!isNil(data[key])) {
      if (Array.isArray(data[key])) {
        newObject[key] = data[key].map((value) => {
          return typeof value === "object" ? filterEmptyData(value) : value;
        });
      } else if (typeof data[key] === "object") {
        newObject[key] = filterEmptyData(data[key]);
      } else {
        newObject[key] = data[key];
      }
    }
  }
  return newObject;
};

export const sleep = (s) =>
  new Promise((resolve) => setTimeout(resolve, s * ONE_SECOND));

export const capitalize = (s): string => {
  if (typeof s !== "string") return "";
  return s.charAt(0).toUpperCase() + s.slice(1).toLowerCase();
};

export const isFunction = (value): boolean =>
  value && typeof value === "function";

export const getRandomNumber = (length: number) => {
  const charset = [..."0123456789"];
  let result = "";
  for (let index = 0; index < length; index++)
    result += charset[Math.floor(Math.random() * charset.length)];
  return result;
};

export const isString = (value: unknown): boolean => typeof value === "string";

export const isStringBoolean = (value): boolean =>
  value === "true" || value === "false" || typeof value == "boolean";

export const normalizeDbObject = (value) => JSON.parse(JSON.stringify(value));

export const normalizeBoolean = (value) =>
  isStringBoolean(value) ? value === "true" : value;

export const convertToUUID = (string_) => {
  const insertString = (string__, index, value) => {
    return (
      string__.slice(0, Math.max(0, index)) + value + string__.slice(index)
    );
  };
  let uuid = string_;
  uuid = insertString(uuid, 8, "-");
  uuid = insertString(uuid, 13, "-");
  uuid = insertString(uuid, 18, "-");
  uuid = insertString(uuid, 23, "-");

  return uuid;
};

export const isJSON = (string_) => {
  try {
    JSON.parse(string_);
    return true;
  } catch {
    return false;
  }
};

export const jsonSafeParse = <T>(
  text: string,
  reviver?: (key: any, value: any) => any
): T => {
  if (typeof text !== "string") return text as T;
  const firstChar = text[0];
  if (firstChar !== "{" && firstChar !== "[" && firstChar !== '"') {
    return text as T;
  }
  try {
    return JSON.parse(text, reviver) as T;
  } catch (error) {
    throw new Error(`Invalid JSON: ${error.message}`);
  }
};

export const safeBooleanParse = (text: string): boolean | null => {
  if (text.trim().toLowerCase() === "true") return true;
  if (text.trim().toLowerCase() === "false") return false;
  return null;
};

export const extractIdFromString = (s: string) => {
  if (!s.includes('"id":')) {
    return null;
  }

  const start = s.indexOf('"id":') + 5;
  const end = s.indexOf(",", start);

  const id = s.substring(start, end);

  return id;
};

export const extractTitleFromString = (s: string) => {
  if (!s.includes('"title":')) {
    return null;
  }

  const start = s.indexOf('"title":') + 8;
  const end = s.indexOf(",", start);
  const title = s.substring(start, end);

  return title.substring(1, title.length - 1);
};

export const createAuthTokens = async (
  userId: number,
  userType: UserType | string,
  fingerPrint?: string
): Promise<TokenInfoType> => {
  if (!userId && !userType) {
    throw new Error("User id and user type is required");
  }

  const accessLifetime = config.get("jwt.accessExpireTime");
  const refreshLifetime = config.get("jwt.refreshExpireTime");

  const accessTokenExpiresAt = new Date();
  const refreshTokenExpiresAt = new Date();

  // Add milliseconds to the dates
  accessTokenExpiresAt.setTime(
    accessTokenExpiresAt.getTime() + parseTimeToMilliseconds(accessLifetime)
  );
  refreshTokenExpiresAt.setTime(
    refreshTokenExpiresAt.getTime() + parseTimeToMilliseconds(refreshLifetime)
  );
  // const { base64url, SignJWT } = await import("jose");
  const accessSecret = base64url.decode(config.get("jwt.accessSecret"));
  const refreshSecret = base64url.decode(config.get("jwt.refreshSecret"));

  const accessToken = await new SignJWT({
    userId,
    userType,
    fingerPrint,
  })
    .setProtectedHeader({ alg: "HS256" })
    .setExpirationTime(accessLifetime)
    .sign(accessSecret);

  const refreshToken = await new SignJWT({ userId, userType })
    .setProtectedHeader({ alg: "HS256" })
    .setExpirationTime(refreshLifetime)
    .sign(refreshSecret);

  if (!accessToken || !refreshToken) {
    throw ClientException.new({
      overrideMessage: "Token not created",
      code: Code.BAD_REQUEST_ERROR,
    });
  }

  return {
    accessToken,
    refreshToken,
    accessTokenExpiresAt,
    refreshTokenExpiresAt,
  };
};
