import { requestHelper } from 'src/helpers/index';
import { logger } from 'src/log';

import { Status } from '../core/common';

export type LocationType = { lat: number; lng: number };

export type GeoResponseType<T> = [string | null, T];

export const getLnLgByAddress = async (
  address,
): Promise<GeoResponseType<LocationType>> => {
  try {
    const result: any = await requestHelper.fetch(
      `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURI(
        address,
      )}&key=${process.env.GOOGLE_MAPS_API_KEY}`,
      {
        method: 'GET',
      },
    );

    if (result.statusText === Status.OK) {
      return [null, result.data.results[0].geometry.location];
    }
    logger.error(`getLnLgByAddress: ${result.statusText}`);
    return [Status[result.status], null];
  } catch (error) {
    logger.error(`getLnLgByAddress: ${error.message}`);
  }
  return [Status.UNKNOWN_ERROR, null];
};

export const getDistance = async (
  origins: string,
  destinations: string,
): Promise<GeoResponseType<{ distance: number; duration: number }>> => {
  try {
    const result: any = await requestHelper.fetch(
      `https://maps.googleapis.com/maps/api/distancematrix/json?origins=${encodeURI(
        origins,
      )}&destinations=${encodeURI(destinations)}&key=${
        process.env.GOOGLE_MAPS_API_KEY
      }`,
      {
        method: 'GET',
      },
    );

    if (result.status === Status.OK) {
      return [
        null,
        {
          distance: result.data.rows[0].elements[0].distance.value,
          duration: result.data.rows[0].elements[0].duration.value,
        },
      ];
    }

    logger.error(`getDistance: ${result.error_message}`);
    return [Status[result.status], null];
  } catch (error) {
    logger.error(`getLnLgByAddress: ${error.message}`);
  }
  return [Status.UNKNOWN_ERROR, null];
};
