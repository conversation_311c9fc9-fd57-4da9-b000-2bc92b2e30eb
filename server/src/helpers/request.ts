import axios, {
  AxiosError,
  AxiosRequestConfig,
  AxiosResponseHeaders,
  RawAxiosResponseHeaders,
} from 'axios';
import { logger } from 'src/log';
import ip3country = require('ip3country');

export interface ApiResponse<T> {
  data: T;
  status: number;
  statusText: string;
  headers: RawAxiosResponseHeaders | AxiosResponseHeaders;
}

export async function fetch<T = any>(
  url: string,
  config?: Omit<AxiosRequestConfig, 'url'>,
  ignoreError = false,
): Promise<ApiResponse<T>> {
  try {
    const response = await axios({
      url,
      ...config,
    });
    return {
      data: response.data,
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
    };
  } catch (error) {
    if (ignoreError) {
      return;
    }
    const axiosError = error as AxiosError;
    logger.error(axiosError.message);
    throw axiosError;
  }
}

export function getCountry(request): string {
  ip3country.init();
  const xForwardedFor =
    process.env.NODE_ENV === 'production'
      ? ((request.ip || request.headers['x-forwarded-for']) as string)
      : (request.headers['x-forwarded-for'] as string);
  return ip3country.lookupStr(xForwardedFor) || null;
}

const requestHelper = {
  fetch,
  getCountry,
};

export default requestHelper;
