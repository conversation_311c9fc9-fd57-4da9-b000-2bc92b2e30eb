import * as fs from 'node:fs';
import * as path from 'node:path';
import { promisify } from 'node:util';

import sizeOf from 'image-size';
import {ClientException, Code, RequestFile, RequestFiles} from 'src/core/common';
import { requestHelper } from 'src/helpers/index';
import isCorrectFilePath from 'src/helpers/path';
import { logger } from 'src/log';

const sizeOfAsync = promisify(sizeOf);
const unlinkAsync = promisify(fs.unlink);
const writeFileAsync = promisify(fs.writeFile);

export const validateFilePath = (filePath: string) => {
  if (!isCorrectFilePath(filePath)) {
    throw ClientException.new({
      overrideMessage: 'Invalid file path',
      code: Code.BAD_REQUEST_ERROR,
    });
  }
};

const download = async (downloadUrl: string, destination: string) => {
  validateFilePath(destination);
  const sanitizedDestination = path.resolve(destination);
  const file = fs.createWriteStream(sanitizedDestination);

  const { data: response } = await requestHelper.fetch(downloadUrl, {
    method: 'GET',
    responseType: 'stream',
  });

  response.pipe(file);

  return new Promise((resolve, reject) => {
    file.on('finish', resolve);
    file.on('error', reject);
  });
};

const getFilenameFromUrl = (link: string) => {
  const parsed = new URL(link);
  return path.basename(parsed.pathname);
};

const existUrl = (checkingUrl: string): Promise<boolean> => {
  return new Promise((resolve) => {
    requestHelper
      .fetch(checkingUrl, {
        method: 'HEAD',
        timeout: 500,
      })
      .then((response) => resolve(!!response.statusText))
      .catch(() => resolve(false));
  });
};

const unlinkSync = async (filePath: string): Promise<void> => {
  validateFilePath(filePath);
  try {
    await unlinkAsync(filePath);
  } catch (error) {
    logger.info({
      message: `Error unlink file ${filePath} with error: ${error.message}`,
      stack: error.stack,
    });
  }
};

const getRequestFile = (
  files: RequestFiles,
  fieldName: string,
): RequestFile | null => {
  if (!files || Object.keys(files).length === 0) {
    return null;
  }
  const filesByFieldName = files[fieldName];
  if (!Array.isArray(filesByFieldName) || filesByFieldName.length === 0) {
    return null;
  }
  return filesByFieldName[0];
};

const writeFileSync = async (filePath, file) => {
  validateFilePath(filePath);
  try {
    await writeFileAsync(filePath, file, { encoding: 'utf8', flag: 'w' });
  } catch (error) {
    logger.error('Error writing JSON to file:', error);
  }
};

const getImageSize = async (
  filePath: string,
): Promise<{ width: number; height: number }> => {
  return await sizeOfAsync(filePath);
};

const fileHelper = {
  download,
  getFilenameFromUrl,
  existUrl,
  unlinkSync,
  getRequestFile,
  writeFileSync,
  getImageSize,
  validateFilePath,
};

export default fileHelper;
