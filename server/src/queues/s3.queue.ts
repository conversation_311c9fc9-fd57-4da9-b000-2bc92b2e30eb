import * as fs from "node:fs";
const Queue = require("bee-queue");
import { PromisePool } from "@supercharge/promise-pool";

import { fileHelper, requestHelper } from "src/helpers";
import * as correlator from "src/helpers/correlationId";
import { logger } from "src/log";
import { uuid } from "uuidv4";

import ConvertQueue from "./convert.queue";
import * as path from "node:path";
const uploadPath = path.resolve(__dirname, "../../");

type TFileData = {
  name: any;
  filePath: string;
  folder: string;
};

const s3Queue = new Queue("s3", {
  redis: { url: process.env.REDISCLOUD_URL },
  isWorker: false,
  removeOnSuccess: true,
  removeOnFailure: true,
});

export default class S3Queue {
  convertQueue: ConvertQueue;

  constructor() {
    this.convertQueue = new ConvertQueue();
  }

  // public async upload(
  //   name: any,
  //   filePath: string,
  //   folder: string,
  //   options: {
  //     optimize?: boolean;
  //     convertTo?: string;
  //     cropImage?: boolean;
  //     width?: number;
  //     height?: number;
  //     ContentType?: string;
  //   } = {
  //     optimize: false,
  //     convertTo: "webp",
  //     cropImage: false,
  //   },
  //   forceUnlink: boolean = true
  // ) {
  //   const body = fs.readFileSync(filePath);
  //   const correlationId = correlator.getId();
  //   const job = s3Queue.createJob({
  //     action: "upload",
  //     name,
  //     body,
  //     folder,
  //     options,
  //     correlationId,
  //   });

  //   return await job
  //     .timeout(120_000)
  //     .retries(0)
  //     .save()
  //     .then((job: any) => {
  //       logger.info(`upload | ${folder} | ${name} | jobId: ${job.id} `);
  //       return new Promise((resolve, reject) => {
  //         job.on("succeeded", async (result: any) => {
  //           return resolve(result);
  //         });
  //         job.on("failed", (error: Error) => {
  //           return reject(error);
  //         });
  //         job.on("error", (error: Error) => {
  //           return reject(error);
  //         });
  //       });
  //     })
  //     .catch((error: Error) => {
  //       logger.error(
  //         `upload | ${folder} | ${name} | jobId: ${job.id} | ${error}`
  //       );
  //       return error;
  //     })
  //     .finally(async () => {
  //       if (forceUnlink) {
  //         const resolvedPath = path.isAbsolute(filePath)
  //           ? filePath
  //           : path.resolve(filePath);
  //         await fileHelper.unlinkSync(resolvedPath);
  //       }
  //     });
  // }

  // public async uploadMultiple(
  //   filesArray: TFileData[],
  //   options: {
  //     optimize?: boolean;
  //     convertTo?: string;
  //     cropImage?: boolean;
  //     width?: number;
  //     height?: number;
  //     ContentType?: string;
  //   } = {
  //     optimize: false,
  //     convertTo: "webp",
  //     cropImage: false,
  //   }
  // ) {
  //   const { results, errors } = await PromisePool.withConcurrency(3)
  //     .for(filesArray)
  //     .process(async ({ name, filePath, folder }) => {
  //       const body = fs.readFileSync(filePath);
  //       const correlationId = correlator.getId();
  //       const job = s3Queue.createJob({
  //         action: "upload",
  //         name,
  //         body,
  //         folder,
  //         options,
  //         correlationId,
  //       });

  //       return await job
  //         .timeout(120_000)
  //         .retries(0)
  //         .save()
  //         .then((job: any) => {
  //           logger.info(`upload | ${folder} | ${name} | jobId: ${job.id} `);
  //           return new Promise((resolve, reject) => {
  //             job.on("succeeded", async (result: any) => {
  //               return resolve(result);
  //             });
  //             job.on("failed", (error: Error) => {
  //               return reject(error);
  //             });
  //             job.on("error", (error: Error) => {
  //               return reject(error);
  //             });
  //           });
  //         })
  //         .catch((error: Error) => {
  //           logger.error(
  //             `upload | ${folder} | ${name} | jobId: ${job.id} | ${error}`
  //           );
  //           return error;
  //         })
  //         .finally(async () => {
  //           await fileHelper.unlinkSync(filePath);
  //         });
  //     });

  //   if (errors.length > 0) {
  //     logger.error(`Some files failed to process: ${errors}`);
  //   }

  //   return results;
  // }

  // public uploadAsyncPlan(
  //   name: any,
  //   filePath: string,
  //   folder: string,
  //   io: any,
  //   planId: number,
  //   convertToWebp: boolean = true
  // ) {
  //   const body = fs.readFileSync(filePath);
  //   const correlationId = correlator.getId();
  //   const job = s3Queue.createJob({
  //     action: "upload",
  //     name,
  //     body,
  //     folder,
  //     optimize: false,
  //     options: {},
  //     convertToWebp,
  //     correlationId,
  //   });
  //   return job
  //     .timeout(120_000)
  //     .retries(0)
  //     .save()
  //     .then((job: any) => {
  //       logger.info(
  //         `uploadAsyncPlan | ${folder} | ${name} | jobId: ${job.id} `
  //       );
  //       return new Promise((resolve, reject) => {
  //         job.on("succeeded", async (result: any) => {
  //           try {
  //             await fileHelper.unlinkSync(filePath);
  //             const plan = await new DbFloorPlan().fromId(planId);
  //             plan.type = name.split(".").pop();
  //             plan.url = result;
  //             await plan.save(plan.tourId);
  //             if (plan.type === "pdf") {
  //               const filename = name.split(".pdf")[0];
  //               const imagePath = await this.convertQueue.pdf2jpg(
  //                 `${filename}`,
  //                 `tours/${plan.tourId}/floorplans`
  //               );
  //               await this.upload(
  //                 `${filename}.jpg`,
  //                 imagePath,
  //                 `tours/${plan.tourId}/floorplans`
  //               );
  //             }
  //             if (plan.floorPlanOrderId) {
  //               const order = await new DbFloorPlanOrder().fromId(
  //                 plan.floorPlanOrderId
  //               );
  //               order.status = FloorPlanOrderStatus.Done;
  //               await order.save(order.tourId);
  //             }
  //             io.emit("plan-upload", plan);
  //             return resolve(result);
  //           } catch (error) {
  //             return reject(error);
  //           }
  //         });
  //         job.on("failed", (error: Error) => {
  //           return reject(error);
  //         });
  //         job.on("error", (error: Error) => {
  //           return reject(error);
  //         });
  //       });
  //     })
  //     .catch(async (error: Error) => {
  //       try {
  //         const plan = await new DbFloorPlan().fromId(planId);

  //         if (plan.floorPlanOrderId) {
  //           const order = await new DbFloorPlanOrder().fromId(
  //             plan.floorPlanOrderId
  //           );
  //           order.status = FloorPlanOrderStatus.Error;
  //           order.error = error.message;
  //           await order.save(order.tourId);
  //         }

  //         await plan.destroy();

  //         logger.error(
  //           `uploadAsyncPlan | ${folder} | ${name} | jobId: ${job.id} | ${error.message}`
  //         );
  //       } catch {
  //         logger.error(
  //           `uploadAsyncPlan | ${folder} | ${name} | jobId: ${job.id} | ${error.message}`
  //         );
  //       }
  //     })
  //     .finally(async () => {
  //       await fileHelper.unlinkSync(filePath);
  //     });
  // }

  // public uploadAsync(
  //   name: any,
  //   filePath: string,
  //   folder: string,
  //   io: any,
  //   imageId: number,
  //   optimize: boolean = true
  // ) {
  //   const body = fs.readFileSync(filePath);
  //   const job = s3Queue.createJob({
  //     action: "upload",
  //     name,
  //     body,
  //     folder,
  //     optimize,
  //     correlationId: correlator.getId(),
  //   });
  //   return job
  //     .timeout(1000)
  //     .retries(0)
  //     .save()
  //     .then((job: any) => {
  //       logger.info(`uploadAsync | ${folder} | ${name} | jobId: ${job.id} `);
  //       return new Promise((resolve, reject) => {
  //         job.on("succeeded", async (result: any) => {
  //           try {
  //             const image = await new DbTourImage().fromId(imageId);
  //             image.url = result;
  //             await image.save(image.tourId);
  //             io.emit("image-upload", image);
  //             return resolve(result);
  //           } catch (error) {
  //             return reject(error);
  //           } finally {
  //             await fileHelper.unlinkSync(filePath);
  //           }
  //         });
  //         job.on("failed", async (error: Error) => {
  //           await fileHelper.unlinkSync(filePath);
  //           return reject(error);
  //         });
  //         job.on("error", async (error: Error) => {
  //           await fileHelper.unlinkSync(filePath);
  //           return reject(error);
  //         });
  //       });
  //     })
  //     .catch(async (error: Error) => {
  //       try {
  //         const image = await new DbTourImage().fromId(imageId);
  //         await image.destroy();
  //         logger.error(
  //           `uploadAsync | ${folder} | ${name} | jobId: ${job.id} | ${error.message}`
  //         );
  //       } catch {
  //         logger.error(
  //           `uploadAsync | ${folder} | ${name} | jobId: ${job.id} | ${error.message}`
  //         );
  //       } finally {
  //         await fileHelper.unlinkSync(filePath);
  //       }
  //     });
  // }

  // private cleanUrl(oldURL: string) {
  //   let index = 0;
  //   let newURL = oldURL;
  //   index = oldURL.indexOf("?");
  //   if (index == -1) {
  //     index = oldURL.indexOf("#");
  //   }
  //   if (index != -1) {
  //     newURL = oldURL.slice(0, Math.max(0, index));
  //   }
  //   return newURL.replace(/\/+$/, "").split("/").pop();
  // }

  // private async getObjectFromUrl(url: string, originalFilename?: string) {
  //   return requestHelper
  //     .fetch(url, { responseType: "arraybuffer", timeout: 300_000 })
  //     .then(async (res) => {
  //       const fileType = this.cleanUrl(url).split(".").pop(); //getting jpg/png
  //       let fileName = uploadPath + "/uploads/" + uuid() + "." + fileType;
  //       if (originalFilename) {
  //         fileName = uploadPath + "/uploads/" + originalFilename;
  //       }
  //       try {
  //         fs.writeFileSync(fileName, res.data);
  //         return fileName;
  //       } catch (error) {
  //         logger.error(
  //           `Error get object from url ${url}. Message: ${error.message}`
  //         );
  //         throw error;
  //       }
  //     })
  //     .catch((error: any) => {
  //       throw error;
  //     });
  // }

  // public async uploadFromSrc(
  //   source_: string,
  //   uploadPath: string,
  //   uploadFileName: string = uuid(),
  //   optimize: boolean = false
  // ) {
  //   try {
  //     const url = source_.startsWith("http")
  //       ? source_
  //       : `${process.env.MEDIA_HOST_URL}/${uploadPath}/${source_}`;
  //     const source: any = await this.getObjectFromUrl(url).catch(() => false);
  //     if (!source) return null;

  //     const result = await this.upload(
  //       uploadFileName, // if not provided, it'll generate uuid
  //       source,
  //       uploadPath, // e.g. "tours/" + tourId + "/images"
  //       {
  //         optimize,
  //       }
  //     );
  //     await fileHelper.unlinkSync(source);
  //     return result;
  //   } catch (error) {
  //     logger.error(`uploadFromSrc | ${error.dbMessage ?? error.message}`);
  //     return error;
  //   }
  // }

  // public async moveFile(source: string, destination: string) {
  //   const job = s3Queue.createJob({
  //     action: "moveFile",
  //     source,
  //     destination,
  //     correlationId: correlator.getId(),
  //   });
  //   return job
  //     .timeout(60_000)
  //     .retries(0)
  //     .save()
  //     .then((job: any) => {
  //       logger.info(
  //         `moveFile | ${source} -> ${destination} | jobId: ${job.id}`
  //       );
  //       return new Promise((resolve, reject) => {
  //         job.on("succeeded", (result: any) => {
  //           return resolve(result);
  //         });
  //         job.on("failed", (error: Error) => {
  //           return reject(error);
  //         });
  //         job.on("error", (error: Error) => {
  //           return reject(error);
  //         });
  //       });
  //     })
  //     .catch((error: Error) => {
  //       logger.error(`moveFile | jobId: ${job.id} | ${error.message}`);
  //       throw error;
  //     });
  // }

  // public async deleteFile(filePath: string) {
  //   const job = s3Queue.createJob({
  //     action: "deleteFile",
  //     path: filePath,
  //     correlationId: correlator.getId(),
  //   });
  //   return job
  //     .timeout(60_000)
  //     .retries(0)
  //     .save()
  //     .then((job: any) => {
  //       logger.info(`deleteFile | ${filePath} | jobId: ${job.id}`);
  //       return new Promise((resolve, reject) => {
  //         job.on("succeeded", (result: any) => {
  //           return resolve(result);
  //         });
  //         job.on("failed", (error: Error) => {
  //           return reject(error);
  //         });
  //         job.on("error", (error: Error) => {
  //           return reject(error);
  //         });
  //       });
  //     })
  //     .catch((error: Error) => {
  //       logger.error(`deleteFile | jobId: ${job.id} | ${error.message}`);
  //       throw error;
  //     });
  // }

  // public async deleteFolder(dir: string, bucketName?: string) {
  //   const job = s3Queue.createJob({
  //     action: "deleteFolder",
  //     dir,
  //     bucketName,
  //     correlationId: correlator.getId(),
  //   });
  //   return job
  //     .timeout(60_000)
  //     .retries(0)
  //     .save()
  //     .then((job: any) => {
  //       logger.info(`deleteFolder | ${dir} | jobId: ${job.id}`);
  //       return new Promise((resolve, reject) => {
  //         job.on("succeeded", (result: any) => {
  //           return resolve(result);
  //         });
  //         job.on("failed", (error: Error) => {
  //           return reject(error);
  //         });
  //         job.on("error", (error: Error) => {
  //           return reject(error);
  //         });
  //       });
  //     })
  //     .catch((error: Error) => {
  //       logger.error(`deleteFolder | jobId: ${job.id} | ${error.message}`);
  //       throw error;
  //     });
  // }
}
