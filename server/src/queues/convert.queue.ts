import * as fs from 'node:fs';

import { requestHelper } from 'src/helpers';
import * as correlator from 'src/helpers/correlationId';
import { logger } from 'src/log';

const Queue = require('bee-queue');
const sourceFolder = require('node:path').resolve(__dirname, '../../');

const convertQueue = new Queue('convert', {
  redis: { url: process.env.REDISCLOUD_URL },
  isWorker: false,
  removeOnSuccess: true,
  removeOnFailure: true,
});

export default class ConvertQueue {
  constructor() {}

  public async pdf2jpg(filename: string, path: string) {
    const job = convertQueue.createJob({
      action: 'pdf2jpg',
      filename: filename,
      path: path,
      correlationId: correlator.getId(),
    });
    return job
      .timeout(120_000)
      .retries(0)
      .save()
      .then((job: any) => {
        logger.info(`pdf2jpg | jobId: ${job.id} | path: ${path}`);
        return new Promise((resolve, reject) => {
          job.on('succeeded', (uri: any) => {
            return requestHelper
              .fetch(uri, { responseType: 'arraybuffer', timeout: 300_000 })
              .then((res) => {
                const filePath = sourceFolder + '/uploads/' + filename + '.jpg';
                fs.writeFileSync(filePath, res.data);
                return resolve('uploads/' + filename + '.jpg');
              })
              .catch((error: any) => {
                return reject(error);
              });
          });
          job.on('failed', (error: Error) => {
            return reject(error);
          });
          job.on('error', (error: Error) => {
            return reject(error);
          });
        });
      })
      .catch((error: Error) => {
        logger.error(`pdf2jpg | jobId: ${job.id} | ${error.message}`);
        throw error;
      });
  }
}
