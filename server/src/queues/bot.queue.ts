import { RESOLUTION } from "src/constants";
import { correlator } from "src/helpers";
import { logger } from "src/log";

const Queue = require("bee-queue");

const botQueue = new Queue("bot", {
  redis: { url: process.env.REDISCLOUD_URL },
  isWorker: false,
  removeOnSuccess: true,
  removeOnFailure: true,
});

const opticalQueue = new Queue("optical", {
  redis: { url: process.env.REDISCLOUD_URL },
  isWorker: false,
  removeOnSuccess: true,
  removeOnFailure: true,
});

export default class BotQueue {
  // public async scanTour({ id, io, status = TourStatus.Ready, auth = false }) {
  //   const job = botQueue.createJob({
  //     action: 'scanTour',
  //     id,
  //     status,
  //     auth,
  //     correlationId: correlator.getId(),
  //   });
  //   return job
  //     .timeout(600_000)
  //     .retries(0)
  //     .save()
  //     .then((job: any) => {
  //       logger.info(`bot | jobId: ${job.id}`);
  //       return new Promise((resolve, reject) => {
  //         job.on('succeeded', async (result: any) => {
  //           io.emit('bot-job-done', result);
  //           return resolve(result);
  //         });
  //         job.on('failed', (error: Error) => {
  //           return reject(error);
  //         });
  //         job.on('error', (error: Error) => {
  //           return reject(error);
  //         });
  //       });
  //     })
  //     .catch(async (error: Error) => {
  //       try {
  //         logger.error(`bot | jobId: ${job.id} | ${error.message}`);
  //         const tour = await new DbTour().fromPk(id);
  //         tour.error = error.message;
  //         tour.status = TourStatus.Error;
  //         await tour.save();
  //         io.emit('bot-job-done', {
  //           matterId: tour.matterId,
  //           slug: tour.slug,
  //           id: tour.id,
  //           updatedAt: tour.updatedAt,
  //           title: tour.title,
  //           status: TourStatus.Error,
  //           error: error.message,
  //           clientId: tour.clientId,
  //           mainImage: tour.mainImage,
  //         });
  //       } catch (error_) {
  //         logger.error(`bot | jobId: ${job.id} | ${error_.message}`);
  //       }
  //     });
  // }
  // public exportTour(tourSlug: string, socketId: any, io: any) {
  //   logger.info(`BotQueue:exportTour | start`);
  //   const job = botQueue.createJob({
  //     action: 'exportTour',
  //     tourSlug,
  //     correlationId: correlator.getId(),
  //   });
  //   return job
  //     .timeout(600_000)
  //     .retries(0)
  //     .save()
  //     .then((job: any) => {
  //       logger.info(`exportTour | jobId: ${job.id}`);
  //       return new Promise((resolve, reject) => {
  //         job.on('succeeded', (result: any) => {
  //           io.to(socketId).emit('bot-export-tour-done', {
  //             tour: result,
  //             socketId,
  //           });
  //           return resolve(result);
  //         });
  //         job.on('failed', (error: Error) => {
  //           return reject(error);
  //         });
  //         job.on('error', (error: Error) => {
  //           return reject(error);
  //         });
  //       });
  //     })
  //     .catch(async (error: Error) => {
  //       logger.error(`bot | jobId: ${job.id} | ${error.message}`);
  //     });
  // }
  // public exportFlow(id: number, tourId: number, socketId: any, io: any) {
  //   logger.info(`BotQueue:exportFlow | start`);
  //   const job = botQueue.createJob({
  //     action: 'exportFlow',
  //     id,
  //     tourId,
  //     correlationId: correlator.getId(),
  //   });
  //   return job
  //     .timeout(600_000)
  //     .retries(0)
  //     .save()
  //     .then((job: any) => {
  //       logger.info(`exportFlow | jobId: ${job.id}`);
  //       return new Promise((resolve, reject) => {
  //         job.on('succeeded', (result: any) => {
  //           io.to(socketId).emit('bot-export-flow-done', {
  //             tour: result,
  //             socketId,
  //           });
  //           return resolve(result);
  //         });
  //         job.on('failed', (error: Error) => {
  //           return reject(error);
  //         });
  //         job.on('error', (error: Error) => {
  //           return reject(error);
  //         });
  //       });
  //     })
  //     .catch(async (error: Error) => {
  //       logger.error(`bot | jobId: ${job.id} | ${error.message}`);
  //     });
  // }
  // public async importTour(tourSlug: string, object: object, io: any) {
  //   logger.info(`BotQueue:importTour | start`);
  //   const job = botQueue.createJob({
  //     action: 'importTour',
  //     tourSlug,
  //     object,
  //     correlationId: correlator.getId(),
  //   });
  //   return job
  //     .timeout(600_000)
  //     .retries(0)
  //     .save()
  //     .then((job: any) => {
  //       logger.info(`importTour | jobId: ${job.id}`);
  //       return new Promise((resolve, reject) => {
  //         job.on('succeeded', async (result: any) => {
  //           io.emit('bot-import-tour-done', result);
  //           return resolve(result);
  //         });
  //         job.on('failed', (error: Error) => {
  //           return reject(error);
  //         });
  //         job.on('error', (error: Error) => {
  //           return reject(error);
  //         });
  //       });
  //     })
  //     .catch(async (error: Error) => {
  //       logger.error(`bot | jobId: ${job.id} | ${error.message}`);
  //     });
  // }
  // public async importFlow(id: number, object: object, io: any) {
  //   logger.info(`BotQueue:importFlow | start`);
  //   const job = botQueue.createJob({
  //     action: 'importFlow',
  //     id,
  //     object,
  //     correlationId: correlator.getId(),
  //   });
  //   return job
  //     .timeout(600_000)
  //     .retries(0)
  //     .save()
  //     .then((job: any) => {
  //       logger.info(`importFlow | jobId: ${job.id}`);
  //       return new Promise((resolve, reject) => {
  //         job.on('succeeded', async (result: any) => {
  //           io.emit('bot-import-flow-done', result);
  //           return resolve(result);
  //         });
  //         job.on('failed', (error: Error) => {
  //           return reject(error);
  //         });
  //         job.on('error', (error: Error) => {
  //           return reject(error);
  //         });
  //       });
  //     })
  //     .catch(async (error: Error) => {
  //       logger.error(`bot | jobId: ${job.id} | ${error.message}`);
  //     });
  // }
  // public importOpticalCenterProducts(tourId: number, io: any) {
  //   logger.info(`opticalQueue:importOpticalCenterProducts | start`);
  //   const job = opticalQueue.createJob({
  //     action: 'importOpticalCenterProducts',
  //     tourId,
  //     correlationId: correlator.getId(),
  //   });
  //   return job
  //     .timeout(600_000)
  //     .retries(0)
  //     .save()
  //     .then((job) => {
  //       logger.info(`importOpticalCenterProducts | jobId: ${job.id}`);
  //       return new Promise((resolve, reject) => {
  //         job.on('succeeded', async (result) => {
  //           io.emit('bot-import-optical-center-products-done', result);
  //           return resolve(result);
  //         });
  //         job.on('failed', (error: Error) => {
  //           return reject(error);
  //         });
  //         job.on('error', (error: Error) => {
  //           return reject(error);
  //         });
  //       });
  //     })
  //     .catch(async (error: Error) => {
  //       logger.error(
  //         `importOpticalCenterProducts | jobId: ${job.id} | ${error.message}`,
  //       );
  //     });
  // }
  // public updateOpticalCenterProducts(tourId: number, io: any) {
  //   logger.info(`opticalQueue:updateOpticalCenterProducts | start`);
  //   const job = opticalQueue.createJob({
  //     action: 'updateOpticalCenterProducts',
  //     tourId,
  //     correlationId: correlator.getId(),
  //   });
  //   return job
  //     .timeout(600_000)
  //     .retries(0)
  //     .save()
  //     .then((job) => {
  //       logger.info(`updateOpticalCenterProducts | jobId: ${job.id}`);
  //       return new Promise((resolve, reject) => {
  //         job.on('succeeded', async (result) => {
  //           io.emit('bot-update-optical-center-products-done', result);
  //           return resolve(result);
  //         });
  //         job.on('failed', (error: Error) => {
  //           return reject(error);
  //         });
  //         job.on('error', (error: Error) => {
  //           return reject(error);
  //         });
  //       });
  //     })
  //     .catch(async (error: Error) => {
  //       logger.error(
  //         `updateOpticalCenterProducts | jobId: ${job.id} | ${error.message}`,
  //       );
  //     });
  // }
  // public async duplicateTour(parentSlug: string, tourSlug: string, io: any) {
  //   const job = botQueue.createJob({
  //     action: 'duplicateTour',
  //     parentSlug,
  //     tourSlug,
  //     correlationId: correlator.getId(),
  //   });
  //   return job
  //     .timeout(600_000)
  //     .retries(0)
  //     .save()
  //     .then((job: any) => {
  //       logger.info(`duplicateTour | jobId: ${job.id}`);
  //       return new Promise((resolve, reject) => {
  //         job.on('succeeded', async (result: any) => {
  //           if (io) io.emit('bot-job-done', result);
  //           return resolve(result);
  //         });
  //         job.on('failed', (error: Error) => {
  //           return reject(error);
  //         });
  //         job.on('error', (error: Error) => {
  //           return reject(error);
  //         });
  //       });
  //     })
  //     .catch(async (error: Error) => {
  //       try {
  //         logger.error(`bot | jobId: ${job.id} | ${error.message}`);
  //         const tour = await new DbTour().fromSlug(tourSlug, { status: '*' });
  //         tour.error = error.message;
  //         tour.status = TourStatus.Error;
  //         await tour.save();
  //         if (io) {
  //           io.emit('bot-job-done', {
  //             matterId: tour.matterId,
  //             slug: tour.slug,
  //             id: tour.id,
  //           });
  //         }
  //       } catch (error_) {
  //         logger.error(`bot | jobId: ${job.id} | ${error_.message}`);
  //       }
  //     });
  // }
  // public async createAnalyticRecord(data: CreateAnalyticType) {
  //   const job = botQueue.createJob({
  //     action: 'createAnalytic',
  //     data,
  //     correlationId: correlator.getId(),
  //   });
  //   return job
  //     .timeout(600_000)
  //     .retries(2)
  //     .save()
  //     .then((job: any) => {
  //       return new Promise((resolve, reject) => {
  //         job.on('succeeded', (result: any) => {
  //           return resolve(result);
  //         });
  //         job.on('failed', (error: Error) => {
  //           return reject(error);
  //         });
  //         job.on('error', (error: Error) => {
  //           return reject(error);
  //         });
  //       });
  //     })
  //     .catch((error: Error) => {
  //       logger.error(
  //         `bot create analytic | jobId: ${job.id} | ${
  //           error.message
  //         } | data: ${JSON.stringify(data)}`,
  //       );
  //       throw error;
  //     });
  // }
  // public async saveGameAnalytic(data: CreateAnalyticType) {
  //   const job = botQueue.createJob({
  //     action: 'saveGameAnalytic',
  //     data,
  //     correlationId: correlator.getId(),
  //   });
  //   return job
  //     .timeout(600_000)
  //     .retries(2)
  //     .save()
  //     .then((job: any) => {
  //       return new Promise((resolve, reject) => {
  //         job.on('succeeded', (result: any) => {
  //           return resolve(result);
  //         });
  //         job.on('failed', (error: Error) => {
  //           return reject(error);
  //         });
  //         job.on('error', (error: Error) => {
  //           return reject(error);
  //         });
  //       });
  //     })
  //     .catch((error: Error) => {
  //       logger.error(
  //         `bot create analytic | jobId: ${job.id} | ${
  //           error.message
  //         } | data: ${JSON.stringify(data)}`,
  //       );
  //       throw error;
  //     });
  // }
  // public async uploadImages(images: any[], tourId: number) {
  //   const [width, height] = RESOLUTION.FULL_HD;
  //   const job = botQueue.createJob({
  //     action: 'uploadImages',
  //     images,
  //     tourId,
  //     options: { width, height },
  //     correlationId: correlator.getId(),
  //   });
  //   return job
  //     .timeout(600_000)
  //     .retries(0)
  //     .save()
  //     .then((job: any) => {
  //       logger.info(`bot upload image | jobId: ${job.id}`);
  //       return new Promise((resolve, reject) => {
  //         job.on('succeeded', (result: any) => {
  //           return resolve(result);
  //         });
  //         job.on('failed', (error: Error) => {
  //           return reject(error);
  //         });
  //         job.on('error', (error: Error) => {
  //           return reject(error);
  //         });
  //       });
  //     })
  //     .catch((error: Error) => {
  //       logger.error(`bot upload image | jobId: ${job.id} | ${error.message}`);
  //       throw error;
  //     });
  // }
  // public async cropMainImage(
  //   tourId: number,
  //   mainImage: string,
  //   isLibrary = false,
  // ) {
  //   const job = botQueue.createJob({
  //     action: 'cropMainImage',
  //     tourId,
  //     mainImage,
  //     isLibrary,
  //     correlationId: correlator.getId(),
  //   });
  //   return job
  //     .timeout(600_000)
  //     .retries(0)
  //     .save()
  //     .then((job: any) => {
  //       logger.info(`bot cropMainImage | jobId: ${job.id}`);
  //       return new Promise((resolve, reject) => {
  //         job.on('succeeded', (result: any) => {
  //           return resolve(result);
  //         });
  //         job.on('failed', (error: Error) => {
  //           return reject(error);
  //         });
  //         job.on('error', (error: Error) => {
  //           return reject(error);
  //         });
  //       });
  //     })
  //     .catch((error: Error) => {
  //       logger.error(`bot cropMainImage | jobId: ${job.id} | ${error.message}`);
  //       throw error;
  //     });
  // }
  // public async saveMinimap(
  //   matterId: number,
  //   tourId: number,
  //   floor: number = null,
  // ) {
  //   const job = botQueue.createJob({
  //     action: 'saveMinimap',
  //     matterId,
  //     tourId,
  //     floor,
  //     correlationId: correlator.getId(),
  //   });
  //   return job
  //     .timeout(600_000)
  //     .retries(0)
  //     .save()
  //     .then((job: any) => {
  //       logger.info(`bot saveMinimap | jobId: ${job.id}`);
  //       return new Promise((resolve, reject) => {
  //         job.on('succeeded', (result: any) => {
  //           return resolve(result);
  //         });
  //         job.on('failed', (error: Error) => {
  //           return reject(error);
  //         });
  //         job.on('error', (error: Error) => {
  //           return reject(error);
  //         });
  //       });
  //     })
  //     .catch((error: Error) => {
  //       logger.error(`bot saveMinimap | jobId: ${job.id} | ${error.message}`);
  //       throw error;
  //     });
  // }
  // public async saveMinimapNew(
  //   matterId: number,
  //   tourId: number,
  //   auth: string = null,
  // ) {
  //   const job = botQueue.createJob({
  //     action: 'saveMinimapNew',
  //     matterId,
  //     tourId,
  //     auth,
  //     correlationId: correlator.getId(),
  //   });
  //   return job
  //     .timeout(600_000)
  //     .retries(0)
  //     .save()
  //     .then((job: any) => {
  //       logger.info(`bot saveMinimapNew | jobId: ${job.id}`);
  //       return new Promise((resolve, reject) => {
  //         job.on('succeeded', (result: any) => {
  //           return resolve(result);
  //         });
  //         job.on('failed', (error: Error) => {
  //           return reject(error);
  //         });
  //         job.on('error', (error: Error) => {
  //           return reject(error);
  //         });
  //       });
  //     })
  //     .catch((error: Error) => {
  //       logger.error(
  //         `bot saveMinimapNew | jobId: ${job.id} | ${error.message}`,
  //       );
  //       throw error;
  //     });
  // }
  // public async passwordLogin(matterId: number, password: string) {
  //   const job = botQueue.createJob({
  //     action: 'passwordLogin',
  //     matterId,
  //     password,
  //     correlationId: correlator.getId(),
  //   });
  //   return job
  //     .timeout(600_000)
  //     .retries(0)
  //     .save()
  //     .then((job: any) => {
  //       logger.info(`bot passwordLogin | jobId: ${job.id}`);
  //       return new Promise((resolve, reject) => {
  //         job.on('succeeded', (result: any) => {
  //           return resolve(result);
  //         });
  //         job.on('failed', (error: Error) => {
  //           return reject(error);
  //         });
  //         job.on('error', (error: Error) => {
  //           return reject(error);
  //         });
  //       });
  //     })
  //     .catch((error: Error) => {
  //       throw error;
  //     });
  // }
  // public async importPanorama(panorama: any, tourSlug: string, io: any) {
  //   const job = botQueue.createJob({
  //     action: 'importPanorama',
  //     panorama,
  //     tourSlug,
  //     correlationId: correlator.getId(),
  //   });
  //   return job
  //     .timeout(600_000)
  //     .retries(0)
  //     .save()
  //     .then((job: any) => {
  //       logger.info(`bot | jobId: ${job.id}`);
  //       return new Promise((resolve, reject) => {
  //         job.on('succeeded', async (result: any) => {
  //           io.emit('bot-job-done', result);
  //           return resolve(result);
  //         });
  //         job.on('failed', (error: Error) => {
  //           return reject(error);
  //         });
  //         job.on('error', (error: Error) => {
  //           return reject(error);
  //         });
  //       });
  //     })
  //     .catch(async (error: Error) => {
  //       try {
  //         logger.error(`bot | jobId: ${job.id} | ${error.message}`);
  //         io.emit('bot-job-done', {});
  //       } catch (error_) {
  //         logger.error(`bot | jobId: ${job.id} | ${error_.message}`);
  //       }
  //     });
  // }
  // public async duplicateFlow(
  //   parentFlowId: number,
  //   flowId: number,
  //   tourId: number,
  // ) {
  //   const job = botQueue.createJob({
  //     action: 'duplicateFlow',
  //     parentFlowId,
  //     flowId,
  //     tourId,
  //     correlationId: correlator.getId(),
  //   });
  //   return job
  //     .timeout(600_000)
  //     .retries(0)
  //     .save()
  //     .then((job: any) => {
  //       logger.info(`bot duplicateFlow | jobId: ${job.id}`);
  //       return new Promise((resolve, reject) => {
  //         job.on('succeeded', (result: any) => {
  //           return resolve(result);
  //         });
  //         job.on('failed', (error: Error) => {
  //           return reject(error);
  //         });
  //         job.on('error', (error: Error) => {
  //           return reject(error);
  //         });
  //       });
  //     })
  //     .catch((error: Error) => {
  //       logger.error(`bot duplicateFlow | jobId: ${job.id} | ${error.message}`);
  //       throw error;
  //     });
  // }
}
