import { Expose, plainToClass } from 'class-transformer';
import { IsDate, IsOptional, IsString, IsUUID } from 'class-validator';

export class AnalyticUserRequestDto {
  @Expose()
  @IsOptional()
  @IsUUID()
  uuid?: string;

  @IsOptional()
  @IsString()
  @Expose()
  public fingerprint?: string;

  @IsOptional()
  @IsString()
  @Expose()
  public platform?: string;

  @IsOptional()
  @IsString()
  @Expose()
  public language?: string;

  @IsOptional()
  @IsString()
  @Expose()
  public architecture?: string;

  @IsOptional()
  @IsString()
  @Expose()
  public userAgent?: string;

  @IsOptional()
  @IsString()
  @Expose()
  public mobileBrand?: string;

  @IsOptional()
  @IsString()
  @Expose()
  public screenResolution?: string;

  @IsOptional()
  @IsString()
  @Expose()
  public platformVersion?: string;

  @IsOptional()
  @IsDate()
  @Expose()
  public createdAt?: Date;

  public static mapFrom<P>(data: Required<P>): AnalyticUserRequestDto {
    const classData = plainToClass(AnalyticUserRequestDto, data, {
      excludeExtraneousValues: true,
    });
    for (const key in classData) {
      if (classData.hasOwnProperty(key) && classData[key] === undefined) {
        delete classData[key];
      }
    }
    return classData;
  }
}
