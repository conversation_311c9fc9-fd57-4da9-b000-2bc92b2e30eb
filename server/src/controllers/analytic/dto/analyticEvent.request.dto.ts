import { Expose, plainToClass } from 'class-transformer';
import { IsOptional, IsString, IsUUID } from 'class-validator';

export class AnalyticEventRequestDto {
  @IsOptional()
  @Expose()
  @IsUUID()
  uuid?: string;

  @IsOptional()
  @IsString()
  @Expose()
  public type?: string;

  @IsOptional()
  @IsString()
  @Expose()
  public documentLocation?: string;

  @IsOptional()
  @IsString()
  @Expose()
  public documentReferer?: string;

  @IsOptional()
  @IsString()
  @Expose()
  public documentTitle?: string;

  @IsOptional()
  @IsString()
  @Expose()
  public tourName?: string;

  @IsOptional()
  @Expose()
  public tourId?: string;

  @IsOptional()
  @IsString()
  @Expose()
  public tourMatterId?: string;

  @IsOptional()
  @IsString()
  @Expose()
  public category?: string;

  @IsOptional()
  @IsString()
  @Expose()
  public action?: string;

  @IsOptional()
  @IsString()
  @Expose()
  public label?: string;

  @IsOptional()
  @IsString()
  @Expose()
  public startDate?: string;

  @IsOptional()
  @IsString()
  @Expose()
  public endDate?: string;

  public static mapFrom<P>(data: Required<P>): AnalyticEventRequestDto {
    const classData = plainToClass(AnalyticEventRequestDto, data, {
      excludeExtraneousValues: true,
    });
    for (const key in classData) {
      if (classData.hasOwnProperty(key) && classData[key] === undefined) {
        delete classData[key];
      }
    }
    return classData;
  }
}
