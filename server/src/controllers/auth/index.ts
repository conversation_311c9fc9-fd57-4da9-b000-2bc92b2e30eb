import { <PERSON>b<PERSON><PERSON>, User } from "../../db/lib/user/user";
import { Client } from "@hubspot/api-client";

import StripePayment from "src/core/service/stripe";
import { BotQueue } from "src/queues";
import { uuid } from "uuidv4";

import config from "../../config";
import { EMAILS, MINUTES } from "../../constants";
import {
  ClientException,
  Code,
  CoreApiResponse,
  EnumQueue,
  handleError,
  UserType,
  validate,
} from "../../core/common";
import {
  getRefreshTokenCookieSettings,
  rateLimiter,
  REFRESH_TOKEN,
} from "../../core/common/util";
import { getHost } from "../../helpers";
import { createAuthTokens, getRandomNumber } from "../../helpers/utils";

import { OAuth2Client } from "google-auth-library";
// import { google } from "googleapis";
import { logger, LogLevel } from "../../log";
// import {
//   addMailchimp,
//   alreadyRegistered,
//   subscribeMailchimp,
// } from '../../routers/util';
// import FolderController from '../folder';
// import TourController from '../tour';

const ct = require("countries-and-timezones");
const passwordHash = require("password-hash");

import * as jose from "jose";
// import axios from "axios";
import { Request, Response } from "express";
import { SignUpRequest } from "src/dto";
const { decodeJwt, jwtVerify, base64url } = jose;

// const TWO_FA_RANDOM_NUMBER = 6;

const oAuth2Client = new OAuth2Client(
  process.env.GOOGLE_CLIENT_ID,
  process.env.GOOGLE_CLIENT_SECRET,
  "postmessage"
);

export interface IDeviceInfo {
  fingerprint?: string;
  userAgent?: string;
  language?: string;
  [key: string]: any;
}

interface OnSuccessAuthParams {
  user?: DbUser;
  origin?: string;
  deviceInfo?: IDeviceInfo;
  role?: UserType;
  tokens?: any | null;
}

interface GoogleDecodedTokenType {
  email: string;
  name: string;
  sub: string;
}

class AuthController {
  queues;

  botQueue: BotQueue;

  stripe: StripePayment;

  // folderController: FolderController;

  constructor(queues) {
    this.queues = queues;
    // this.stripe = new StripePayment({ apiKey: config.get("stripe.apiKey") });
    // this.folderController = new FolderController();
    this.botQueue = new BotQueue();
    // this.tourController = new TourController({
    //   [EnumQueue.Bot]: this.botQueue,
    // });
  }

  // private async updateHubspotContact(
  //   email: string,
  //   properties: Record<string, any>,
  //   skipCreate?: boolean
  // ) {
  //   try {
  //     const hubspotClient = new Client({
  //       accessToken: process.env.HUBSPOT_API_KEY,
  //     });

  //     try {
  //       await hubspotClient.crm.contacts.basicApi.update(
  //         email,
  //         { properties },
  //         "email"
  //       );
  //       logger.info(`Hubspot contact ${email} updated`);
  //     } catch (error) {
  //       if (error.body.message === "resource not found" && !skipCreate) {
  //         // Failed to update contact which means it doesn't exist in Hubspot and we create it
  //         await hubspotClient.crm.contacts.basicApi.create({
  //           properties: { email: email, ...properties },
  //         });
  //         logger.info(`Hubspot contact ${email} created`);
  //       } else {
  //         throw error;
  //       }
  //     }
  //   } catch (error) {
  //     logger.info(`Failed to update Hubspot contact ${error.message}`);
  //   }
  // }

  // private async handleEmailSchedules(userId: number) {
  //   try {
  //     console.log("Handling email schedules for user:", userId);
  //     const emailSchedules = await new DbEmailSchedules();

  //     // if prod then 1 unit is 1 day and if dev then 1 unit is 1 hour
  //     const unit =
  //       process.env.NODE_ENV === "production"
  //         ? 24 * 60 * 60 * 1000
  //         : 60 * 60 * 1000;

  //     const emails = Object.values(EMAILS) as [
  //       {
  //         free: string;
  //         growth: string;
  //         business: string;
  //         key: string;
  //         onDay?: number;
  //       }
  //     ];

  //     for (const email of emails) {
  //       if (email.onDay) {
  //         emailSchedules.addEntry({
  //           sendDate: new Date(Date.now() + email.onDay * unit),
  //           postmarkFreeId: email.free,
  //           postmarkGrowthId: email.growth,
  //           postmarkBusinessId: email.business,
  //           key: email.key,
  //           userId,
  //         });
  //       }
  //     }
  //   } catch (error) {
  //     console.error("Error handling email schedules:", error);
  //   }
  // }

  @handleError("auth/getUser")
  public async getUser(req: Request, res: Response) {
    try {
      const user = await new DbUser().getById(req.account.userId);
      delete user.password;
      return res.json(CoreApiResponse.success(user));
    } catch (error) {
      logger.error(`auth/me: ${error.overrideMessage || error.message}`);
      throw ClientException.new({
        overrideMessage: error.message,
        code: Code.BAD_REQUEST_ERROR,
      });
    }
  }

  @validate(SignUpRequest)
  @handleError("auth/signup")
  public async signup(req: Request, res: Response) {
    const data = req.body;
    const originDomain = req.get("origin");
    try {
      const origin = getHost(originDomain);
      const domains = ["gridevo.com"];
      if (!domains.includes(origin) && process.env.NODE_ENV === "production") {
        throw ClientException.new({
          overrideMessage: "Sign up is not allowed from this domain",
          code: Code.BAD_REQUEST_ERROR,
        });
      }

      const user = new DbUser();
      Object.assign(user, data);

      user.email = data.email.toLowerCase();
      user.password = passwordHash.generate(user.password);
      // const emailToken = uuid();
      // user.emailToken = emailToken;

      if (await user.checkExisting(user.email)) {
        throw ClientException.new({
          overrideMessage: "User with this email already exists",
          code: Code.BAD_REQUEST_ERROR,
        });
      }

      // let refererUser;
      // if (data.refer) {
      //   refererUser = await new DbUser().fromUsername(data.refer);
      //   user.refer = refererUser ? refererUser.id : null;
      // }
      await user.save();

      // if (refererUser) {
      //   await this.queues[EnumQueue.Email].sendEmail(
      //     "referUser",
      //     config.get("mail.from"),
      //     refererUser.email,
      //     {
      //       name: user.name,
      //       email: user.email,
      //       phone: user.phone,
      //       country: user.country,
      //       company: user.company,
      //       mode: user.mode,
      //     }
      //   );
      // }

      // await this.queues[EnumQueue.Email].sendEmail(
      //   "confirmEmail",
      //   config.get("mail.from"),
      //   user.email,
      //   {
      //     name: user.name,
      //     token: emailToken,
      //   }
      // );

      // Upsert user in Hubspot
      // if (process.env.NODE_ENV === "production") {
      //   const properties = {
      //     firstname: data.firstName,
      //     lastname: data.lastName,
      //     full_name: data.firstName + " " + data.lastName,
      //     company: data.company,
      //     country: data.country,
      //     phone: data.phone,
      //     is_matterport_service_provider: data.mpServiceProvider ? "yes" : "no",
      //     numemployees: data.numberOfEmployees || 0,
      //     industry_new: data.industry || "",
      //     website: data.website || "",
      //     job_description: data.jobDescription || "",
      //     existing_3d_digital_twin: data.existing3DDigitalTwin ? "yes" : "no",
      //     hear_place: data.hearPlace || "",
      //     newsletter_enabled: data.newsletterEnabled ? "yes" : "no",
      //     gridevo_admin_sign_up: "yes",
      //     gridevo_sign_up_date: new Date().setUTCHours(0, 0, 0, 0),
      //   };

      //   // Updating hubspot contact
      //   // await this.updateHubspotContact(data.email, properties);
      // }

      // await this.handleEmailSchedules(user.id);

      if (process.env.NODE_ENV !== "production") {
        return {
          // confirmEmailURL: `http://localhost:3000/admin/confirm-email/${emailToken}`,
        };
      }

      return res
        .status(Code.SUCCESS.code)
        .json({ message: Code.SUCCESS.message });
    } catch (error) {
      logger.error(`auth/signup: ${error.overrideMessage || error.message}`);
      throw ClientException.new({
        overrideMessage: error.message,
        code: Code.BAD_REQUEST_ERROR,
      });
    }
  }

  public async confirmEmail(data, data2) {}
  public async forgotPassword(data) {}

  @rateLimiter.decorator("email")
  public async userLogin(req: Request, res: Response) {
    const deviceInfo = {
      ...req.body.deviceInfo,
      fingerprint: req.get("X-Device-Fingerprint"),
    };
    const { email, password } = req.body;

    const origin = req.get("origin");

    try {
      if (!(email && password)) {
        throw ClientException.new({
          overrideMessage: "Missing email/password",
          code: Code.BAD_REQUEST_ERROR,
        });
      }

      const isUserExist = await new DbUser().checkExisting(email.toLowerCase());
      if (!isUserExist) {
        throw ClientException.new({
          overrideMessage: "Wrong password or email",
          code: Code.BAD_REQUEST_ERROR,
          metadata: { logLevel: LogLevel.INFO },
        });
      }

      const user = await new DbUser().getUserByEmail(email.toLowerCase());

      if (!user) {
        throw ClientException.new({
          overrideMessage: "Wrong password or email",
          code: Code.BAD_REQUEST_ERROR,
          metadata: { logLevel: LogLevel.INFO },
        });
      }
      const matchedPass = passwordHash.verify(password, user.password);
      if (!matchedPass) {
        throw ClientException.new({
          overrideMessage: "Wrong password or email",
          code: Code.BAD_REQUEST_ERROR,
          metadata: { logLevel: LogLevel.INFO },
        });
      }

      // this.isConfirmedUser(user);

      // if (user.twoFA) {
      //   user.pinTwoFA = getRandomNumber(TWO_FA_RANDOM_NUMBER);
      //   this.queues[EnumQueue.Email].sendEmail(
      //     "sendAuthPin",
      //     config.get("mail.from"),
      //     user.email,
      //     {
      //       name: user.name,
      //       pin: user.pinTwoFA,
      //     }
      //   );
      //   await user.save();
      //   return { user, token: null };
      // }

      // await this.metricService.trackEvent({
      //   user,
      //   event: MetricEvent.UserActive,
      //   eventData: { last_active_at: Date.now() },
      // });

      let response = await this.onSuccessAuth({
        user,
        origin,
        deviceInfo,
        role: UserType.Admin,
      });

      if (response.user?.twoFA) {
        response = { twoFA: true };
      }

      const { refreshToken, ...restOfResponse } = response;
      res.cookie(REFRESH_TOKEN, refreshToken, getRefreshTokenCookieSettings());

      return res.json(CoreApiResponse.success(restOfResponse));
    } catch (error) {
      logger.error(`auth/login: ${error.overrideMessage || error.message}`);
      throw ClientException.new({
        overrideMessage: error.message,
        code: Code.BAD_REQUEST_ERROR,
      });
    }
  }

  public async googleLogin(data) {
    try {
      const { tokens } = await oAuth2Client.getToken(data.code);
      const decodedIdToken: GoogleDecodedTokenType = decodeJwt(tokens.id_token);
      if (decodedIdToken) {
        const email = decodedIdToken.email;
        const user = await new DbUser().getUserByEmail(email, {
          disableCatchError: true,
        });
        if (user) {
          return await this.onSuccessAuth({
            user,
            origin: data.origin,
            deviceInfo: data.deviceInfo,
            role: UserType.Admin,
          });
        }
        const newUser = new DbUser();
        newUser.name = decodedIdToken.name;
        newUser.email = decodedIdToken.email;
        newUser.googleSub = decodedIdToken.sub;
        newUser.confirmed = true;
        const savedUser = await newUser.save();
        // return await newUser.save();
        return await this.onSuccessAuth({
          user: savedUser,
          origin: data.origin,
          deviceInfo: data.deviceInfo,
          role: UserType.Admin,
          tokens,
        });
      }
    } catch (error) {
      logger.error(
        `auth/googleLogin: ${error.overrideMessage || error.message}`
      );
      throw ClientException.new({
        overrideMessage: error.message,
        code: Code.BAD_REQUEST_ERROR,
      });
    }
  }

  public async onSuccessAuth({
    user,
    origin = null,
    deviceInfo = {},
    role = UserType.Admin,
  }: OnSuccessAuthParams): Promise<AuthSession> {
    const tokensInfo = await createAuthTokens(user.id, user.type ?? role);

    delete user.password;
    if (user.country) {
      const timezones = ct.getTimezonesForCountry(user.country);
      if (timezones && timezones.length > 0) {
        user.timezone = timezones[0].utcOffset / MINUTES;
      }
    }

    // const session = await this.createAuthSession({
    //   user,
    //   tokensInfo,
    //   deviceInfo,
    //   role,
    // });
    return {
      ...tokensInfo,
      // ...session,
      user,
    };
  }

  public async logOutUser(req: Request, res: Response) {
    try {
      const refreshToken = req.cookies[REFRESH_TOKEN];
      if (refreshToken) {
        // const decoded = jwt.decodeJwt(refreshToken);
        // const idField = getSessionIdField(decoded.userType);
        // const fingerprint = request.headers["x-device-fingerprint"];

        // if (decoded?.userId) {
        //   const session = await new DbAuthSession().get({
        //     query: {
        //       [idField]: decoded.userId,
        //       device: { [db.Sequelize.Op.contains]: { fingerprint } },
        //     },
        //     disableCatchError: true,
        //   });

        //   if (session) {
        //     await session.destroy();
        //   }
        // }

        res.clearCookie(REFRESH_TOKEN);
      }

      return res.json(CoreApiResponse.success());
    } catch (error) {
      return res.json(
        CoreApiResponse.error(
          Code.BAD_REQUEST_ERROR.code,
          error?.message || Code.BAD_REQUEST_ERROR.message
        )
      );
    }
  }
}

export default AuthController;
