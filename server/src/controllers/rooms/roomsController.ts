import { CoreApiResponse, handleError } from "src/core/common";
import { DbRoomCategory } from "src/db/lib/property/roomCategory";

class RoomsController {
  queues;

  constructor(queues) {
    this.queues = queues;
  }

  @handleError("rooms/getCategoriesWithRooms")
  public async getCategoriesWithRooms(req, res) {
    try {
      const categories = await new DbRoomCategory().getRoomCategoriesWithRooms(
        2
      );
      return res.json(CoreApiResponse.success(categories));
    } catch (error) {
      return null;
    }
  }

  public async addRoomCategory(data) {
    try {
      const category = new DbRoomCategory();
      Object.assign(category, data);
      const savedCategory = await category.save();
      return savedCategory;
    } catch (error) {
      return null;
    }
  }
}

export default RoomsController;
