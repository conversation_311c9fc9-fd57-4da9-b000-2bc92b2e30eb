import * as fs from "node:fs";

import { Client } from "@hubspot/api-client";

import config from "src/config";
import StripePayment from "src/core/service/stripe";
import { requestHelper } from "src/helpers";
import { uuid } from "uuidv4";

import {
  ClientException,
  Code,
  EnumQueue,
  HttpUserPayload,
  password,
  UserAccess,
} from "../../core/common";
// import {
//   cancelPaypalSubscription,
//   subscribeMailchimp,
//   unsubscribeMailchimp,
// } from "../../routers/util";
// import TranslationController from "../translation";
const passwordHash = require("password-hash");

import // GetYad2UserResponse,
// SearchUserByAddressRequest,
// SearchUserByNameRequest,
"src/dto";
import { filterEmptyData } from "src/helpers/utils";

import { logger, LogLevel } from "src/log";
// import TourController from "../tour";

class UserController {
  queues;

  // translationService: TranslationController;

  stripe: StripePayment;

  zendeskAuth: string;

  constructor(queues) {
    this.queues = queues;
    // this.translationService = new TranslationController();
    this.stripe = new StripePayment({ apiKey: config.get("stripe.apiKey") });
    this.zendeskAuth = Buffer.from(
      `${config.get("zendesk.email")}/token:${config.get("zendesk.token")}`
    ).toString("base64");
  }

  // public async add(data, adminId = null) {
  //   try {
  //     const user = new DbUser();
  //     user.name = data.name || null;
  //     user.email = data.email?.toLowerCase().trim() || null;
  //     user.phone = data.phone || null;
  //     user.company = data.company || null;
  //     user.website = data.website || null;
  //     user.country = data.country || null;
  //     user.type = data.type || UserType.User;
  //     user.subscription = "free";
  //     user.toursLimit = data.toursLimit || 5;
  //     user.username = data.username || null;
  //     user.domainUrl = data.domainUrl || null;
  //     user.adminUrl = data.adminUrl || null;
  //     user.password = passwordHash.generate(data.password);
  //     user.emailToken = uuid();
  //     user.confirmed = true;
  //     user.adminId = adminId;
  //     user.newsletterEnabled = false;
  //     user.subscription = UserSubscription.Free;
  //     user.access = data.access || UserAccess.Editor;

  //     if (user.adminId === 1110) {
  //       user.subscription = UserSubscription.Year;
  //     }

  //     if (await user.checkExisting(user.email)) {
  //       throw ClientException.new({
  //         code: Code.BAD_REQUEST_ERROR,
  //         overrideMessage: "User with this email already exists",
  //       });
  //     }

  //     if (adminId) {
  //       // Custom logic for specific admin user
  //       const adminUser = await new DbUser().fromId(adminId);

  //       if (adminUser?.email === "<EMAIL>") {
  //         this.queues[EnumQueue.Email].sendEmail(
  //           "welcomeToVirtualz",
  //           "<EMAIL>",
  //           user.email,
  //           {
  //             name: user.name,
  //           }
  //         );
  //       }
  //     }

  //     user.stripeId = await this.stripe.createCustomer({
  //       name: user.name,
  //       email: user.email,
  //       phone: user.phone,
  //     });

  //     await user.save();

  //     if (!user.confirmed) {
  //       this.queues[EnumQueue.Email].sendEmail(
  //         "confirmEmail",
  //         process.env.GRIDEVO_EMAIL,
  //         user.email,
  //         {
  //           name: user.name,
  //           token: user.emailToken,
  //         }
  //       );
  //     }

  //     if (adminId) {
  //       const translations = await this.translationService.get(
  //         adminId,
  //         UserGroup.User
  //       );
  //       if (translations.length > 0) {
  //         await this.translationService.createMultiple({
  //           userGroup: UserGroup.User,
  //           translations,
  //           id: user.id,
  //         });
  //       }
  //     }

  //     logger.info(`user/add: id ${user.id}`);
  //     delete user.password;
  //     return user;
  //   } catch (error) {
  //     if (error instanceof ClientException) throw error;
  //     logger.error(`UserController>>create. Message: ${error.message}`);
  //     throw ClientException.new({
  //       overrideMessage: "Error create user",
  //       code: Code.BAD_REQUEST_ERROR,
  //     });
  //   }
  // }

  // public async edit({ data, account, file }) {
  //   try {
  //     this.checkAdminAccess(data.id, account);

  //     if (data.password && process.env.NODE_ENV === "production") {
  //       const [error] = password.isStrong(data.password);
  //       if (error) {
  //         throw ClientException.new({
  //           overrideMessage: error,
  //           code: Code.BAD_REQUEST_ERROR,
  //           metadata: { logLevel: LogLevel.INFO },
  //         });
  //       }
  //     }
  //     const user = await this.getUser(data.id, account);

  //     if (user.email === data.email) {
  //       delete data.email;
  //     }

  //     if (
  //       user.subscriptionId !== data.subscriptionId &&
  //       data.subscriptionId?.includes("sub_")
  //     ) {
  //       //subscription changed
  //       const stripeSub: any = await this.stripe.getSubscription(
  //         data.subscriptionId
  //       );
  //       const subscription = this.stripe.getPlan(stripeSub.plan?.id);
  //       if (
  //         subscription === "growth" ||
  //         subscription === "business" ||
  //         subscription === "enterprise"
  //       ) {
  //         user.subscription = subscription;
  //       }
  //     }

  //     for (const key of Object.keys(data as User)) {
  //       if (data[key] != undefined) {
  //         if (key == "type" && account.type != UserType.SuperAdmin) continue;
  //         if (key == "toursLimit" && account.type != UserType.SuperAdmin) continue;
  //         if (key == "email" && account.type != UserType.SuperAdmin) continue;
  //         if (key == "videoCredits" && account.type != UserType.SuperAdmin)
  //           continue;
  //         if (key == "subscription") continue;
  //         if (key == "confirmed") continue;
  //         if (key == "whitelabelConfig" && account.type != UserType.SuperAdmin)
  //           continue;
  //         if (key == "mode" && account.type != UserType.SuperAdmin) continue;
  //         if (key == "subscriptionStart") continue;
  //         if (key == "whitelabelId" && account.type != UserType.SuperAdmin)
  //           continue;
  //         if (key == "cloudfrontId" && account.type != UserType.SuperAdmin)
  //           continue;
  //         if (key == "enterprise" && account.type != UserType.SuperAdmin) continue;
  //         user[key] = data[key];
  //         if (user[key] == "null" || user[key].length === 0) user[key] = null;
  //       }
  //     }

  //     if (user?.stripeId) {
  //       await this.stripe.updateCustomer(user.stripeId, {
  //         name: user.name,
  //         email: user.email,
  //         phone: user.phone,
  //       });
  //     }

  //     if (user.taxId) {
  //       await this.stripe.updateTax(user.stripeId, user.country, user.taxId);
  //     }

  //     if (data.password && data.password != "null") {
  //       user.password = passwordHash.generate(data.password);
  //     }

  //     try {
  //       if (data.newsletterEnabled && data.newsletterEnabled !== "false")
  //         await subscribeMailchimp(user.email, user.name);
  //     } catch (error) {
  //       logger.error(
  //         `UserController>>edit>>${data.id}. Message: ${error.message}`
  //       );
  //     }
  //     await user.save();
  //     const profile = file;
  //     if (profile?.path) {
  //       user.profile = await this.queues[EnumQueue.Storage].upload(
  //         `profile.jpg`,
  //         profile.path,
  //         `users/${user.id}`
  //       );
  //       await user.save();
  //     }
  //     const logo = file?.logo ? file.logo[0] : null;
  //     if (logo) {
  //       user.logo = await this.queues[EnumQueue.Storage].upload(
  //         `yad2logo.jpg`,
  //         logo.path,
  //         `users/${user.id}`,
  //         { optimize: false }
  //       );
  //       await user.save();
  //     }
  //     logger.info(`user/edit: id ${user.id}`);
  //     if (typeof user.permissions === "string")
  //       user.permissions = JSON.parse(user.permissions);
  //     return user;
  //   } catch (error) {
  //     if (error instanceof ClientException) throw error;
  //     logger.error(
  //       `UserController>>edit>>${data.id}. Message: ${error.message}`
  //     );
  //     throw ClientException.new({
  //       overrideMessage: "Error update user",
  //       code: Code.BAD_REQUEST_ERROR,
  //     });
  //   }
  // }

  // // Deactivate addons for users if they switch to a lower plan
  // public async deactivateUserAddons(userId: number) {
  //   const user = await new DbUser().fromId(userId);

  //   // Enterprise users can have unlimited addons
  //   if (user.subscription === UserSubscription.Enterprise) return;

  //   const maxLimit = user.subscription === UserSubscription.Business ? 4 : 0;

  //   let addonCount = 0;

  //   const tours = await new DbTour().getAllByUser(user.id);

  //   const freeAddons = {
  //     freeAR: user.freeAR,
  //     freeExperience: user.freeExperience,
  //     freeVCommerce: user.freeVCommerce,
  //     isFreeFlows: user.isFreeFlows,
  //     freeNavigation: user.freeNavigation,
  //   };

  //   for (const tour of tours) {
  //     if (tour.isSample) continue;
  //     const tourHasAddons =
  //       tour.ar || tour.mode === TourMode.ECommerce || tour.gameId;

  //     if (tourHasAddons) addonCount++;

  //     // if user has reached the limit of addons, deactivate addons
  //     // if the tour has subscriptionId, it means it's an addon bought on stripe and we leave those
  //     if (addonCount > maxLimit && tourHasAddons && !tour.subscriptionId) {
  //       await new TourController({}).deactivateTourAddons(
  //         tour.id,
  //         userId,
  //         freeAddons
  //       );
  //     }

  //     for (const duplicate of tour.duplicates) {
  //       if (duplicate.isSample) continue;
  //       const tourHasAddons =
  //         duplicate.ar ||
  //         duplicate.mode === TourMode.ECommerce ||
  //         duplicate.gameId;

  //       if (tourHasAddons) addonCount++;
  //       // if user has reached the limit of addons, deactivate addons
  //       if (
  //         addonCount > maxLimit &&
  //         tourHasAddons &&
  //         !duplicate.subscriptionId
  //       ) {
  //         await new TourController({}).deactivateTourAddons(
  //           duplicate.id,
  //           userId,
  //           freeAddons
  //         );
  //       }
  //     }
  //   }
  // }

  // private shouldNotCountAsCredit(tour: any, excludeTourId?: number): boolean {
  //   return (
  //     tour.id === excludeTourId ||
  //     tour.isSample ||
  //     new Date(tour.createdAt) < new Date("2024-06-08")
  //   );
  // }

  // public async delete(id: number, account: HttpUserPayload) {
  //   try {
  //     this.checkDeleteAccess(id, account);
  //     const user = await this.getUser(id, account);
  //     const tours = await new DbTour().getAllByUser(user.id);
  //     const clients = await new DbClient().getAllByUser(user.id);
  //     if (user.subscriptionId?.includes("I-") && !user.manualSubscription)
  //       await cancelPaypalSubscription(user.subscriptionId);

  //     if (user.subscriptionId?.includes("sub_"))
  //       await this.stripe.deleteSubscription(user.subscriptionId);

  //     if (user.whitelabelId?.includes("I-"))
  //       await cancelPaypalSubscription(user.whitelabelId);

  //     if (user.whitelabelId?.includes("sub_"))
  //       await this.stripe.deleteSubscription(user.whitelabelId);

  //     if (user.newsletterEnabled) await unsubscribeMailchimp(user.email);
  //     const fullTours = await Promise.all(
  //       tours.map((tour) => {
  //         return new DbTour().fromSlug(tour.slug, { status: "*" });
  //       })
  //     );
  //     await Promise.all(
  //       fullTours
  //         .filter((tour) => tour.users.length < 2)
  //         .map(async (tour: DbTour) => {
  //           if (tour.subscriptionId?.includes("I-")) {
  //             await cancelPaypalSubscription(tour.subscriptionId);
  //           } else if (tour.subscriptionId?.includes("sub_")) {
  //             await this.stripe.deleteSubscription(tour.subscriptionId);
  //           }
  //           await this.queues[EnumQueue.Storage].deleteFolder(
  //             `tours/${tour.id}`
  //           );
  //           return tour.destroy();
  //         })
  //     );

  //     await Promise.all(
  //       clients.map(async (client: DbClient) => {
  //         await this.queues[EnumQueue.Storage].deleteFolder(
  //           `clients/${client.id}`
  //         );
  //         return client.destroy();
  //       })
  //     );
  //     await this.queues[EnumQueue.Storage].deleteFolder(`users/${id}`);
  //     const email = user.email;
  //     await user.destroy();
  //     logger.info(`user/delete: ${id} - ${email} deleted by: ${account.id}`);
  //   } catch (error) {
  //     if (error instanceof ClientException) {
  //       throw error;
  //     }
  //     logger.error(`UserController>>delete>>${id}. Message: ${error.message}`);
  //     throw ClientException.new({
  //       overrideMessage: "Error delete user",
  //       code: Code.BAD_REQUEST_ERROR,
  //     });
  //   }
  // }

  // static async getByApiKey(apiKey) {
  //   return new DbUser().fromAPIKey(apiKey);
  // }

  // // TODO need to be changed after old crm is gone
  // async updatePermissions(
  //   id,
  //   {
  //     permissions,
  //     account,
  //     isNewAdmin,
  //   }: { account: HttpUserPayload; permissions: any; isNewAdmin?: boolean }
  // ) {
  //   try {
  //     await this.getUser(id, account);
  //     return new DbUser().updatePermissions(id, permissions, isNewAdmin);
  //   } catch (error) {
  //     logger.error(
  //       `UserController>>updatePermissions. Message: ${error.message}`
  //     );
  //     throw ClientException.new({
  //       overrideMessage: "Error update permission",
  //       code: Code.BAD_REQUEST_ERROR,
  //     });
  //   }
  // }

  // async editSubscription(id: number, newSubscription: UserSubscription) {
  //   const user = await new DbUser().fromId(id);
  //   user.subscription = newSubscription;
  //   user.subscriptionStart = new Date();
  //   await user.save();
  // }

  // async getPermissions(id: number) {
  //   try {
  //     const userPermissions = await new DbUserPermission().getByUser(id);
  //     return userPermissions.reduce((accumulator, item) => {
  //       accumulator.push(item.permission);
  //       return accumulator;
  //     }, []);
  //   } catch (error) {
  //     logger.error(`UserController>>getPermissions. Message: ${error.message}`);
  //     throw ClientException.new({
  //       overrideMessage: "Error get permissions",
  //       code: Code.BAD_REQUEST_ERROR,
  //     });
  //   }
  // }

  // // TODO need to be changed after old crm is gone
  // async get(id: number, account: HttpUserPayload, isNewAdmin: boolean = false) {
  //   if (account.type === UserType.User && account.id !== id) {
  //     throw ClientException.new({
  //       overrideMessage: "User not exists",
  //       code: Code.BAD_REQUEST_ERROR,
  //     });
  //   }

  //   const user = await this.getUser(id, account, isNewAdmin);

  //   const rescricted =
  //     user.type === UserType.SuperAdmin && account.type !== UserType.SuperAdmin;

  //   if (rescricted) {
  //     throw ClientException.new({
  //       code: Code.ACCESS_DENIED_ERROR,
  //     });
  //   }

  //   delete user.password;

  //   const translation = await new DbCrmTranslation().getAllForUIByUser(user.id);

  //   if (process.env.NODE_ENV != "local") {
  //     let domain = "";

  //     if (user?.domainUrl) {
  //       domain = user.domainUrl;
  //     }

  //     if (process.env.NODE_ENV == "production") {
  //       domain = user?.isNewAdmin
  //         ? user.domainUrl || "my.gridevo.com"
  //         : user.adminUrl || "admin.gridevo.com";
  //     } else {
  //       domain = user?.isNewAdmin
  //         ? "test.gridevo.com"
  //         : "admintest.gridevo.com";
  //     }

  //     user.redirectUrl = user?.isNewAdmin
  //       ? `https://${domain}/admin/`
  //       : `https://${domain}`;
  //   }

  //   logger.info(`user/get: id ${user.id}`);

  //   return { ...user, translation };
  // }

  // async getDNSRecords(id: number, account: HttpUserPayload) {
  //   const user = await this.getUser(id, account);
  //   const dnsRecords = await this.queues[
  //     EnumQueue.Aws
  //   ].getDNSRecordsByUserDomain(user.domainUrl, user.cloudfrontId);
  //   logger.info(`user/getDNSRecords: id ${user.id}`);
  //   return { ...dnsRecords };
  // }

  // async removeDNSRecords(id: number, account: HttpUserPayload) {
  //   const user = await this.getUser(id, account);
  //   await this.queues[EnumQueue.Aws].removeDNSRecords(
  //     user.domainUrl,
  //     user.cloudfrontId
  //   );
  //   logger.info(`user/removeDNSRecords: id ${user.id}`);
  // }

  // async getUserAdmin(id: number, account: HttpUserPayload) {
  //   const user = await new DbUser().fromIdWithAdmin(account.id);
  //   if (id !== user.adminId) {
  //     throw ClientException.new({
  //       overrideMessage: "User not exists",
  //       code: Code.BAD_REQUEST_ERROR,
  //     });
  //   }

  //   logger.info(`user/getUserAdmin: id ${user.id}`);
  //   return user;
  // }

  // private async getUser(
  //   id: number,
  //   account: HttpUserPayload,
  //   isNewAdmin?: boolean
  // ) {
  //   const user = await new DbUser().fromId(id, isNewAdmin);
  //   if (
  //     account.type === UserType.Admin &&
  //     account.selfId !== user.adminId &&
  //     UserType.User === user.type
  //   ) {
  //     throw ClientException.new({
  //       overrideMessage: "Failed to fetch user",
  //       code: Code.BAD_REQUEST_ERROR,
  //     });
  //   }

  //   return user;
  // }

  // private checkAdminAccess(id, account) {
  //   if (
  //     account.type != UserType.Admin &&
  //     account.type != UserType.SuperAdmin &&
  //     account.id != id
  //   ) {
  //     throw ClientException.new({
  //       overrideMessage: "Not allowed",
  //       code: Code.BAD_REQUEST_ERROR,
  //     });
  //   }
  // }

  // private checkDeleteAccess(id, account) {
  //   if (
  //     account.type != UserType.Admin &&
  //     account.type != UserType.User &&
  //     account.type != UserType.SuperAdmin &&
  //     account.id != id
  //   ) {
  //     //Users that are not admin can only delete themselves
  //     throw ClientException.new({
  //       overrideMessage: "Not allowed",
  //       code: Code.BAD_REQUEST_ERROR,
  //     });
  //   }
  // }

  // public async transferHubMinutes({
  //   account,
  //   clientId,
  //   hubMinutes = 0,
  //   type,
  // }: {
  //   account: { id: number; type: UserType };
  //   clientId: number;
  //   type: string;
  //   hubMinutes?: number | string;
  // }) {
  //   const user = await new DbUser().fromId(account.id);
  //   const client =
  //     type === "user"
  //       ? await new DbUser().fromId(clientId)
  //       : await new DbClient().fromPk(clientId);

  //   if (
  //     account.type !== UserType.SuperAdmin &&
  //     account.type !== UserType.Admin &&
  //     client.userId !== account.id
  //   ) {
  //     throw ClientException.new({
  //       code: Code.ACCESS_DENIED_ERROR,
  //     });
  //   }

  //   if (user.hubMinutes < hubMinutes) {
  //     throw ClientException.new({
  //       code: Code.VALIDATION_ERROR,
  //       overrideMessage: "No sufficent hubMinutes",
  //     });
  //   }

  //   user.hubMinutes -= Number.parseInt(hubMinutes.toString(), 10);
  //   client.hubMinutes += Number.parseInt(hubMinutes.toString(), 10);

  //   await user.save();
  //   await client.save(user.id);
  //   logger.info(
  //     `user/credits/transfer: id ${user.id}: ${hubMinutes} -> ${client.id}`
  //   );

  //   return user.videoCredits;
  // }

  // public async updateHubspot({
  //   email,
  //   properties,
  // }: {
  //   email: string;
  //   properties: any;
  // }) {
  //   if (email && properties) {
  //     const hubspotClient = new Client({
  //       accessToken: process.env.HUBSPOT_API_KEY,
  //     });

  //     await hubspotClient.crm.contacts.basicApi
  //       .update(
  //         email,
  //         {
  //           properties,
  //         },
  //         "email"
  //       )
  //       .then((response) => {
  //         logger.info("Hubspot update user: " + email);
  //         return response;
  //       })
  //       .catch((error) => {
  //         if (error.code !== 404) {
  //           logger.error(
  //             `Error update user(${email}) in Hubspot: ${JSON.stringify(error)}`
  //           );
  //         }
  //       });
  //   }
  // }

  // public async createZendeskTicket(ticket: any, files: Express.Multer.File[]) {
  //   const uploads = await Promise.all(
  //     (files || []).map(async (file) => {
  //       const fileData = fs.readFileSync(file.path);

  //       const response = await requestHelper.fetch(
  //         `https://gridevo.zendesk.com/api/v2/uploads.json?filename=${file.originalname}`,
  //         {
  //           data: fileData,
  //           headers: {
  //             "Content-Type": file.mimetype,
  //             Authorization: `Basic ${this.zendeskAuth}`,
  //           },
  //           method: "POST",
  //         }
  //       );

  //       return response?.data?.upload?.token;
  //     })
  //   );

  //   const data = {
  //     ticket: {
  //       subject: ticket.subject,
  //       comment: {
  //         body: ticket.description,
  //         uploads,
  //       },
  //       requester: ticket.requester,
  //     },
  //   };

  //   const response = await requestHelper.fetch(
  //     `https://gridevo.zendesk.com/api/v2/tickets.json`,
  //     {
  //       data: JSON.stringify(data),
  //       headers: {
  //         Accept: "application/json",
  //         "Content-Type": "application/json",
  //         Authorization: `Basic ${this.zendeskAuth}`,
  //       },
  //       method: "POST",
  //     }
  //   );

  //   return response;
  // }

  // async getUsersForHubspot(since?: number) {
  //   const users = await new DbUser().getByTimeSinceLastEdit(since || 1000);

  //   logger.info(`user/getUsersForHubspot: found ${users.length} users`);
  //   return users;
  // }

  // async saveCustomLoginSettings(userId: number, data: any, files: any) {
  //   const updateData = { ...data, userId };

  //   const logoFileData = files?.logo ? files.logo[0] : null;
  //   const backgroundImageFileData = files?.backgroundImage
  //     ? files.backgroundImage[0]
  //     : null;

  //   const userLoginSettings = await new DbUserLoginDesignSettings().get({
  //     query: { userId },
  //     disableCatchError: true,
  //   });

  //   if (userLoginSettings) {
  //     if (userLoginSettings.logoUrl && (logoFileData || data.logo === "null")) {
  //       await this.queues[EnumQueue.Storage].deleteFile(
  //         `users/${userId}/${userLoginSettings.logoUrl}`
  //       );
  //       updateData.logoUrl = null;
  //     }
  //     if (
  //       userLoginSettings.backgroundUrl &&
  //       (backgroundImageFileData || data.backgroundImage === "null")
  //     ) {
  //       await this.queues[EnumQueue.Storage].deleteFile(
  //         `users/${userId}/${userLoginSettings.backgroundUrl}`
  //       );
  //       updateData.backgroundUrl = null;
  //     }
  //   }

  //   if (logoFileData) {
  //     const logoName = `${uuid()}.png`;
  //     const logoUrl = await this.queues[EnumQueue.Storage].upload(
  //       logoName,
  //       logoFileData.path,
  //       `users/${userId}`
  //     );
  //     updateData.logoUrl = logoUrl;
  //   }

  //   if (backgroundImageFileData) {
  //     const backgroundImageName = `${uuid()}.png`;
  //     const backgroundUrl = await this.queues[EnumQueue.Storage].upload(
  //       backgroundImageName,
  //       backgroundImageFileData.path,
  //       `users/${userId}`
  //     );
  //     updateData.backgroundUrl = backgroundUrl;
  //   }

  //   await new DbUserLoginDesignSettings().createOrSave(updateData);
  // }

  // public async getAllYad2Public(): Promise<GetYad2UserResponse[]> {
  //   return await new DbUser().getAllYad2Users();
  // }

  // public async searchByName(
  //   data: SearchUserByNameRequest
  // ): Promise<GetYad2UserResponse[]> {
  //   return await new DbUser().searchByName(data);
  // }

  // public async searchByAddress(
  //   data: SearchUserByAddressRequest
  // ): Promise<GetYad2UserResponse[]> {
  //   const filteredData = filterEmptyData(data);
  //   return await new DbUser().searchByAddress(filteredData);
  // }
}

export default UserController;
