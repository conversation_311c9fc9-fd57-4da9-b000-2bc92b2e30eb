"use strict";

module.exports = {
  async up(queryInterface, sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      await queryInterface.createTable(
        "users",
        {
          id: {
            type: sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false,
          },
          name: {
            type: sequelize.STRING(255),
            allowNull: false,
            defaultValue: "",
          },
          email: {
            type: sequelize.STRING(255),
            allowNull: false,
            unique: true,
            defaultValue: "",
            validate: {
              isEmail: true,
            },
          },
          password: {
            type: sequelize.STRING(255),
            allowNull: false,
            defaultValue: "",
          },
          type: {
            type: sequelize.ENUM("super_admin", "admin", "manager", "employee"),
            allowNull: false,
            defaultValue: "admin",
          },
          confirmed: {
            type: sequelize.BOOLEAN,
            allowNull: false,
            defaultValue: false,
          },
          country: {
            type: sequelize.STRING(100),
            allowNull: false,
            defaultValue: "",
          },
          company: {
            type: sequelize.STRING(255),
            allowNull: false,
            defaultValue: "",
          },
          phone: {
            type: sequelize.STRING(50),
            allowNull: false,
            defaultValue: "",
          },
          twoFaEnabled: {
            type: sequelize.BOOLEAN,
            allowNull: false,
            defaultValue: false,
          },
          subscriptionStart: {
            type: sequelize.DATE,
            allowNull: true,
          },
          subscriptionCycle: {
            type: sequelize.ENUM("month", "year"),
            allowNull: true,
          },
          googleSub: {
            type: sequelize.STRING(255),
            allowNull: true,
          },
          createdAt: {
            type: sequelize.DATE,
            allowNull: false,
            defaultValue: sequelize.literal("CURRENT_TIMESTAMP"),
          },
          updatedAt: {
            type: sequelize.DATE,
            allowNull: false,
            defaultValue: sequelize.literal("CURRENT_TIMESTAMP"),
          },
        },
        { transaction }
      );

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      console.error("Migration failed:", error);
      throw error;
    }
  },

  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      await queryInterface.dropTable("users", { transaction });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      console.error("Migration rollback failed:", error);
      throw error;
    }
  },
};
