// migrations/XXXXXX-create-room-categories.js
"use strict";

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.createTable(
        "roomCategories",
        {
          id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
          },
          enabled: {
            type: Sequelize.BOOLEAN,
            allowNull: false,
            defaultValue: true,
          },
          name: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          shortName: {
            type: Sequelize.STRING(10),
            allowNull: false,
          },

          mainPlaces: {
            type: Sequelize.INTEGER,
            allowNull: false,
            defaultValue: 1,
          },
          additionalPlaces: {
            type: Sequelize.INTEGER,
            allowNull: false,
            defaultValue: 0,
          },
          propertyId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
              model: "properties",
              key: "id",
            },
            onUpdate: "CASCADE",
            onDelete: "CASCADE",
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
          },
        },
        { transaction }
      );

      await queryInterface.addIndex("roomCategories", ["propertyId"], {
        transaction,
      });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.dropTable("roomCategories", { transaction });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
