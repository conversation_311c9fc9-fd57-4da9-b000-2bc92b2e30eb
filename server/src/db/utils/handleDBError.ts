import { logger } from "../../log";
import {
  ExceptionsCode,
  InternalServerError,
  TimeoutDatabaseError,
} from "./exceptions";

export { BadDataError } from "./exceptions/badDataError";

export default function handleDBError(): any {
  return function (
    target: Object,
    key: string | symbol,
    descriptor: PropertyDescriptor
  ) {
    const original = descriptor.value;
    descriptor.value = async function (...args: any[]) {
      try {
        const result = await original.apply(this, args);
        return result;
      } catch (error) {
        const errorObject = {
          class: target?.constructor?.name,
          method: key?.toString(),
          isDatabase: true,
        };
        if (error.status !== 404) {
          logger.error({
            ...errorObject,
            status: error.status,
            message: error.message,
            stack: error.stack,
          });
        }

        if (Number.parseInt(error?.parent?.code) === ExceptionsCode.TIMEOUT) {
          throw new TimeoutDatabaseError(
            JSON.stringify({
              ...errorObject,
              name: "TIMEOUT_ERROR",
            })
          );
        }
        if (error instanceof InternalServerError) {
          throw new InternalServerError(
            JSON.stringify({
              ...errorObject,
              message: error.message,
              name: "DB_ERROR",
            })
          );
        }
        throw error;
      }
    };

    return descriptor;
  };
}
