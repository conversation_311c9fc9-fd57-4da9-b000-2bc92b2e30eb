import { BaseError } from './baseError';

export class InternalServerError extends BaseError {
  public status: number;

  public isInternalError: boolean;

  public message: string;

  public overrideMessage: string;

  constructor(overrideMessage: string) {
    super();
    this.message = 'Internal server error.';
    this.status = 500;
    this.isInternalError = true;
    this.overrideMessage = overrideMessage;
    Error.captureStackTrace(this, this.constructor);
  }
}
