const db = require("../../models");
import { Model } from "../model";
import handleDBError, { BadDataError } from "../../utils/handleDBError";

export class RoomCategory {
  id?: number;

  enabled: boolean;

  name: string;

  shortName: string;

  mainPlaces: number;

  additionalPlaces: number;

  propertyId: number;

  constructor() {
    this.name = "";
    this.enabled = true;
    this.mainPlaces = 2;
  }
}

export class DbRoomCategory extends Model<DbRoomCategory, RoomCategory> {
  constructor() {
    super(db.roomCategory, new RoomCategory());
  }

  @handleDBError()
  async getRoomCategoriesWithRooms(propertyId) {
    try {
      const categories = await this.getAll({
        where: { propertyId },
        include: [
          {
            model: db.room,
            as: "rooms",
          },
        ],
      });
      return categories;
    } catch (error) {}
  }
}
