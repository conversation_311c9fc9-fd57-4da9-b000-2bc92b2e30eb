import { Model } from "../model";
import handleDBError, { BadDataError } from "../../utils/handleDBError";
import { UserType } from "src/core/common";
import * as db from "../../models";

const Op = db.Sequelize.Op;

export enum UserSubscriptionCycle {
  Month = "month",
  Year = "year",
}

export class User {
  id?: number;

  name: string;

  email: string;

  password: string;

  type: UserType;

  confirmed: boolean;

  country: string;

  company: string;

  phone: string;

  twoFaEnabled: boolean;

  subscriptionStart: Date;

  subscriptionCycle: UserSubscriptionCycle;

  googleSub?: string;

  constructor() {
    this.name = "";
    this.email = "";
    this.password = "";
    this.type = UserType.Admin;
    this.confirmed = false;
    this.country = "";
    this.company = "";
    this.twoFaEnabled = false;
    this.googleSub = "";
  }
}

export class DbUser extends Model<DbUser, User> {
  declare name: string;

  declare email: string;

  declare password: string;

  declare confirmed: boolean;

  declare googleSub: string;

  constructor() {
    super(db.user, new User());
  }

  @handleDBError()
  async getByTimeSinceSignup(amount: number) {
    const type = process.env.NODE_ENV === "production" ? 24 : 1; //24h production, 1h test
    const now = new Date();
    const startDate = new Date(now);
    const endDate = new Date(now);
    startDate.setHours(-type * amount, 0, 0, 0); // Set the start of the day X days ago
    endDate.setHours(-type * amount + type, 0, 0, -1); // Set the end of the day X days ago

    return await db.user.findAll({
      where: {
        adminId: 1,
        createdAt: {
          [Op.between]: [startDate, endDate],
        },
      },
      attributes: ["id", "email", "subscription", "name"],
    });
  }

  // Get users that edited their account in the period between X and Y days ago
  @handleDBError()
  async getByTimeSinceLastEdit(days: number, fields: string[] = []) {
    const daysMs = Date.now() - days * 24 * 60 * 60 * 1000;

    return await db.user.findAll({
      where: {
        adminId: 1,
        updatedAt: {
          [Op.lt]: new Date(),
          [Op.gt]: new Date(daysMs),
        },
      },
      attributes: [
        "id",
        "name",
        "email",
        "company",
        "country",
        "createdAt",
        ...fields,
      ],
    });
  }

  @handleDBError()
  async getUserByEmail(email: string, params?: { disableCatchError: boolean }) {
    const record = await this.get({
      where: { email: email.toLowerCase() },
    });
    if (record == undefined) {
      if (params?.disableCatchError) return null;
      throw new BadDataError(404, `Failed to fetch user`);
    }
    return record;
  }

  @handleDBError()
  async checkExisting(email: string, id?: number) {
    const record = await db.user.findOne({
      where: {
        email: email.toLowerCase(),
        id: { [Op.ne]: id || null }, // check existing if user try to change his email we should see if other user exists with same new email
      },
    });

    return record != undefined;
  }

  @handleDBError()
  async search(data) {
    let orderBy = ["id"];
    let orderByType = "DESC";

    if (data?.sortByName) {
      orderBy = [data.sortByName];
      orderByType = data.sortByDesc ? "DESC" : "ASC";
    }

    const { search, link } = data.filter;

    const params: any = {
      where: {
        type: {
          [Op.or]: [UserType.Admin, UserType.Manager],
        },
      },
      attributes: [
        "id",
        "name",
        "email",
        "phone",
        "company",
        "country",
        "confirmed",
      ],
    };

    if (data.adminId || data.refer) {
      const orConditions = [];

      // if (data.adminId) {
      //   orConditions.push({ adminId: data.adminId });
      // }

      // if (data.refer) {
      //   orConditions.push({ refer: data.refer });
      // }

      if (orConditions.length > 0) {
        params.where[Op.or] = orConditions;
      }
    }

    if (search) {
      params.where[Op.or] = [
        {
          name: {
            [Op.iLike]: `%${search}%`,
          },
        },
        {
          email: {
            [Op.iLike]: `%${search}%`,
          },
        },
        {
          company: {
            [Op.iLike]: `%${search}%`,
          },
        },
        {
          country: {
            [Op.iLike]: `%${search}%`,
          },
        },
        {
          domainUrl: {
            [Op.iLike]: `%${search}%`,
          },
        },
        {
          adminUrl: {
            [Op.iLike]: `%${search}%`,
          },
        },
        {
          phone: {
            [Op.iLike]: `%${search}%`,
          },
        },
        {
          subscriptionId: {
            [Op.iLike]: `%${search}%`,
          },
        },
        /*{
          "$clients.subscriptionId$": {
            [Op.iLike]: `%${search}%`,
          },
        },
        {
          "$tours.subscriptionId$": {
            [Op.iLike]: `%${search}%`,
          },
        },*/
      ];
    }
    if (link) {
      params.include = [
        {
          model: db.tour,
          as: "tours",
          where: {
            [Op.or]: [
              {
                slug: {
                  [Op.iLike]: `%${link}%`,
                },
              },
              {
                matterId: {
                  [Op.iLike]: `%${link}%`,
                },
              },
            ],
          },
        },
      ];
    }
    const users = await db.user.findAndCountAll({
      ...params,
      limit: data.limit,
      offset: data.page * data.limit,
      order: [[...orderBy, orderByType]],
    });

    return {
      data: users.rows,
      page: data.page,
      pageSize: data.limit,
      total: users.count,
    };
  }

  @handleDBError()
  public async searchByName(data: Record<string, unknown>) {
    const { searchString } = data;

    const whereObject: Record<string, unknown> = {
      isYad2: true,
      name: {
        [Op.iLike]: `%${searchString}%`,
      },
    };

    const users = await db.user.findAll({
      attributes: [
        "id",
        "logo",
        "name",
        [
          db.sequelize.literal("CAST(COUNT(projects.id) AS INTEGER)"),
          "projectCount",
        ],
      ],
      include: [
        {
          model: db.project,
          as: "projects",
          attributes: [],
          through: { attributes: [] },
        },
      ],
      where: whereObject,
      group: ["user.id"],
      order: [[db.sequelize.literal("COUNT(projects.id)"), "DESC"]],
    });

    return users;
  }
}
