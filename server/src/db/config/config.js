const productionConfig = {
  dialect: "postgres",
  username: process.env.DB_USERNAME || "postgres",
  password: process.env.DB_PASSWORD || "",
  database: process.env.DB_DATABASE || "gridevo",
  host: process.env.DB_HOST || "127.0.0.1",
  logging: false,
  pool: {
    max: 20,
    idle: 30000,
  },
  dialectOptions: {
    statement_timeout: 10000,
    idle_in_transaction_session_timeout: 10000,
  },
};

module.exports = {
  development: {
    username: process.env.DEV_DB_USERNAME || "postgres",
    password: process.env.DEV_DB_PASSWORD || "",
    database: process.env.DEV_DB_DATABASE || "gridevo",
    host: process.env.DEV_DB_HOST || "127.0.0.1",
    dialect: "postgres",
    logging: false,
    dialectOptions: {
      statement_timeout: 20000,
      idle_in_transaction_session_timeout: 20000,
    },
  },
  testing: {
    username: process.env.DEV_DB_USERNAME || "postgres",
    password: process.env.DEV_DB_PASSWORD || "",
    database: process.env.DEV_DB_DATABASE || "gridevo_testing",
    logging: false,
    host: process.env.DEV_DB_HOST || "127.0.0.1",
    dialect: "postgres",
    dialectOptions: {
      statement_timeout: 5000,
      idle_in_transaction_session_timeout: 5000,
    },
  },
  staging: {
    username: "postgres",
    password: null,
    database: "gridevo_staging",
    host: "127.0.0.1",
    logging: false,
    dialect: "postgres",
    dialectOptions: {
      statement_timeout: 5000,
      idle_in_transaction_session_timeout: 5000,
    },
  },
  production: productionConfig,
};
