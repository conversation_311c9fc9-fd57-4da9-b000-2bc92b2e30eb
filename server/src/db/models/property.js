// models/property.js
"use strict";
module.exports = (sequelize, DataTypes) => {
  const property = sequelize.define(
    "property",
    {
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      userId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          table: "users",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
      },
    },
    {}
  );

  property.associate = function (models) {
    property.belongsTo(models.user, {
      foreignKey: "userId",
      as: "property",
      onDelete: "CASCADE",
      onUpdate: "CASCADE",
    });

    property.hasMany(models.roomCategory, {
      foreignKey: "propertyId",
      as: "roomCategories",
      onDelete: "CASCADE",
      onUpdate: "CASCADE",
    });
  };

  return property;
};
