// models/room.js
"use strict";
module.exports = (sequelize, DataTypes) => {
  const room = sequelize.define(
    "room",
    {
      roomNumber: {
        type: DataTypes.STRING(10),
        allowNull: false,
        unique: true,
      },
      building: {
        type: DataTypes.STRING,
        defaultValue: "Main",
      },
      floor: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      categoryId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          table: "roomCategories",
          key: "id",
        },
      },
    },
    {}
  );

  room.associate = function (models) {
    room.belongsTo(models.roomCategory, {
      foreignKey: "categoryId",
      as: "roomCategory",
      onDelete: "CASCADE",
      onUpdate: "CASCADE",
    });
  };

  return room;
};
