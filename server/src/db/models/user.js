"use strict";
module.exports = (sequelize, DataTypes) => {
  var user = sequelize.define(
    "user",
    {
      name: { type: DataTypes.STRING, allowNull: false },
      email: { type: DataTypes.STRING, allowNull: false },
      country: DataTypes.STRING,
      password: DataTypes.STRING,
      confirmed: { type: DataTypes.BOOLEAN, defaultValue: false },
      subscriptionStart: { type: DataTypes.DATE, allowNull: false },
      subscriptionCycle: DataTypes.ENUM("month", "year"),
      type: DataTypes.ENUM("admin", "superAdmin", "manager", "employee"),
      company: DataTypes.STRING,
      phone: DataTypes.STRING,
      twoFaEnabled: { type: DataTypes.BOOLEAN, defaultValue: false },
      googleSub: { type: DataTypes.STRING, allowNull: true },
    },
    {}
  );

  user.associate = function (models) {
    user.hasMany(models.property, {
      foreignKey: "userId",
      as: "properties",
      onDelete: "CASCADE",
      onUpdate: "CASCADE",
    });
  };
  return user;
};
