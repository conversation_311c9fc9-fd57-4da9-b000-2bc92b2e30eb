// models/roomCategory.js
"use strict";
module.exports = (sequelize, DataTypes) => {
  const roomCategory = sequelize.define(
    "roomCategory",
    {
      enabled: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      shortName: {
        type: DataTypes.STRING(10),
        allowNull: false,
      },

      mainPlaces: {
        type: DataTypes.INTEGER,
        defaultValue: 2,
      },
      additionalPlaces: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      propertyId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "properties",
          key: "id",
        },
      },
    },
    {}
  );

  roomCategory.associate = function (models) {
    roomCategory.belongsTo(models.property, {
      foreignKey: "propertyId",
      as: "property",
      onDelete: "CASCADE",
      onUpdate: "CASCADE",
    });

    roomCategory.hasMany(models.room, {
      foreignKey: "categoryId",
      as: "rooms",
      onDelete: "CASCADE",
      onUpdate: "CASCADE",
    });
  };

  return roomCategory;
};
