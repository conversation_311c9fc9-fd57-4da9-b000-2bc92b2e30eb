import * as winston from 'winston';

import * as correlation from './helpers/correlationId';

export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  VERBOSE = 'verbose',
  DEBUG = 'debug',
  SILLY = 'silly',
}

const defaultLevel = LogLevel.INFO;
const getCorrelationId = correlation.getId;
const noCorrelationIdValue = 'nocorrelation';

const logger = winston.createLogger();

const format = winston.format.combine(
  winston.format((info) => {
    if (info.correlationId) {
      return {
        ...info,
        correlationId: info.correlationId,
      };
    } else {
      info.correlationId = getCorrelationId() || noCorrelationIdValue;
    }
    return info;
  })(),
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss',
  }),
  winston.format.errors({ stack: true }),
  winston.format.colorize(),
  winston.format.printf(({ timestamp, correlationId, level, message }) => {
    return `${timestamp} (${correlationId}) - ${level}: ${message}`;
  }),
);

logger.add(
  new winston.transports.Console({
    level: defaultLevel,
    format,
  }),
);

if (process.env.NODE_ENV === 'production') {
  logger.add(
    new winston.transports.File({
      level: defaultLevel,
      format,
      filename: 'server.log',
      maxsize: 1e8,
      maxFiles: 10,
      tailable: true,
    }),
  );
}

export { logger };
