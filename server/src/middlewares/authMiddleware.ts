import config from "src/config";
import { NextFunction, Request, Response } from "express";
import * as jose from "jose";
import {
  Code,
  CoreApiResponse,
  getRefreshTokenCookieSettings,
  REFRESH_TOKEN,
  UserType,
} from "src/core/common";
import { createAuthTokens } from "src/helpers/utils";
import { logger } from "src/log";

const { base64url, jwtVerify } = jose;
const AUTHORIZATION_HEADER = "Authorization";

const authMiddleware = (
  request: Request,
  res: Response,
  next: NextFunction
) => {
  (async () => {
    try {
      const JWT_SECRET = config.get("jwt.accessSecret");
      const authHeader = request.get(AUTHORIZATION_HEADER);
      const refreshToken = request.cookies[REFRESH_TOKEN];

      // ==== 1. Hard Check: Refresh Token Required ====
      if (!refreshToken) {
        return res
          .status(Code.UNAUTHORIZED_ERROR.code)
          .json(
            CoreApiResponse.error(
              Code.UNAUTHORIZED_ERROR.code,
              Code.UNAUTHORIZED_ERROR.message
            )
          );
      }

      // ==== 2. Access Token Validation ====
      if (authHeader) {
        // Only accept properly formatted Bearer tokens
        const bearerRegex = /^bearer\s+((?:[\w=-]+\.){2}[\w+./=-]*)$/i;
        const bearerMatch = bearerRegex.exec(authHeader);
        if (!bearerMatch) {
          return res.status(401).json({
            message: "Invalid authorization format",
          });
        }

        const token = bearerMatch[1];

        try {
          const { payload } = await jwtVerify(
            token,
            base64url.decode(JWT_SECRET)
          );
          request.account = {
            userId: payload.userId as number,
            userType: payload.userType as UserType,
          };
          return next(); // Valid token → proceed
        } catch (error) {
          // IMPORTANT: Only proceed to refresh token flow if token is expired
          // This prevents attackers from getting new tokens by sending invalid tokens
          if (error.code !== "ERR_JWT_EXPIRED") {
            return res.status(401).json({
              message: "Invalid access token. Please login again.",
            });
          }
        }
      } else {
        // No auth header provided - don't allow refresh token flow
        // This prevents attackers from skipping the access token check
        return res.status(401).json({
          message: "Authorization header required",
        });
      }

      // ==== 3. Refresh Token Flow - ONLY for expired access tokens ====
      try {
        const { payload } = await jwtVerify(
          refreshToken,
          base64url.decode(config.get("jwt.refreshSecret"))
        );

        const decoded = {
          userId: payload.userId as number,
          userType: payload.userType as UserType,
        };

        // Issue new tokens (rotate refresh token)
        const { accessToken, refreshToken: newRefreshToken } =
          await createAuthTokens(decoded.userId, decoded.userType);

        res.setHeader("x-access-token", accessToken);
        res.cookie(
          REFRESH_TOKEN,
          newRefreshToken,
          getRefreshTokenCookieSettings()
        );
        request.account = decoded;

        return next();
      } catch (error) {
        // Refresh token invalid → force login
        logger.debug("Refresh token validation failed", {
          error: error.message,
        });
        return res.status(401).json({
          message: "Your session has expired. Please login again.",
        });
      }
    } catch (error) {
      // Global error handler for unexpected errors
      logger.error("Authentication middleware error", { error: error.message });
      return res.status(500).json({
        message: "An unexpected error occurred during authentication",
      });
    }
  })();
};

export default authMiddleware;
