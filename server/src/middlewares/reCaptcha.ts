import { NextFunction, Request, Response } from "express";
import { logger } from "src/log";

import config from "../config";
import { ClientException, Code } from "../core/common";
import { requestHelper } from "../helpers";

const reCaptchaMiddleware = async (
  request: Request,
  res: Response,
  next: NextFunction
) => {
  const { captchaToken: token } = request.body;
  if (!config.get("captcha.enabled")) return next();

  try {
    if (!token) {
      throw ClientException.new({
        code: Code.BAD_REQUEST_ERROR,
        overrideMessage: "Captcha is required",
      });
    }

    const data = {
      secret: config.get("captcha.secret"),
      response: token,
    };

    const response: any = await requestHelper.fetch(
      `https://www.google.com/recaptcha/api/siteverify`,
      {
        headers: { "content-type": "application/x-www-form-urlencoded" },
        method: "POST",
        data,
      }
    );

    if (!response.data.success) {
      throw ClientException.new({
        code: Code.BAD_REQUEST_ERROR,
        overrideMessage: `Captcha is invalid: ${
          response["error-codes"]?.toString() || ""
        }`,
      });
    }
    next();
  } catch (error) {
    logger.error(`middleware/recaptcha: ${error.message}`);
    res.status(Code.BAD_REQUEST_ERROR.code).json({
      message:
        error instanceof ClientException
          ? error.message
          : Code.BAD_REQUEST_ERROR.message,
    });
  }
};

export default reCaptchaMiddleware;
