import * as morgan from "morgan";
import { logger } from "src/log";

const requestLoggingConfig = {
  stream: {
    write: (text) => logger.info(text.trim()),
  },
  skip: function (request, res) {
    return (
      res.statusCode === 200 ||
      res.statusCode === 204 ||
      request.method === "OPTIONS" ||
      request.url === "/"
    );
  },
};

const errorLoggingConfig = {
  stream: {
    write: (text: string) => logger.error(text.trim()),
  },
};

export const requestLogging = [
  morgan(`request: :remote-addr :method :url`, {
    ...requestLoggingConfig,
    immediate: true,
  }),
  morgan(
    `response: :method :status :url (:res[content-length] bytes) :response-time ms`,
    { ...requestLoggingConfig, immediate: false }
  ),
];

export const errorLogging = morgan(
  `ERROR: :remote-addr :method :url :status (:res[content-length] bytes) :response-time ms`,
  { ...errorLoggingConfig, immediate: false }
);

export const infoLogging = morgan(
  `ERROR: :remote-addr :method :url :status (:res[content-length] bytes) :response-time ms`,
  { ...requestLoggingConfig, immediate: false }
);
