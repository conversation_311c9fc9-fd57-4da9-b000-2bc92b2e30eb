{"name": "server", "version": "1.0.0", "description": "gridevo server", "scripts": {"fullstart": "npm run compile && cross-env NODE_ENV=development  npm start", "dev": "cross-env NODE_ENV=development  tsnd --transpile-only -r tsconfig-paths/register --respawn src/www/index.ts", "dev:test": "cross-env NODE_ENV=test PORT=3051 npm run dev", "dev:test_all": "concurrently \"npm run dev:test\" \"cd ../crm & npm run test:start\" \"sleep 60 & cd ../crm & npx cypress run --spec \"cypress/integration/user/client_crud.spec.js\"\"", "build": "npm run compile", "start": "node dist/www/", "compile": "npx tsc && tsc-alias", "test": "cross-env DOTENV_CONFIG_PATH=.env.test jest --setupFiles=dotenv/config", "covarage": "jest --coverage", "debug": "npm run compile && nodemon -L --exec node --inspect=0.0.0.0 --require ts-node/register --require ./src/www/index.ts", "preinstall": "npm run co:login", "prei": "npm run co:login", "prepare": "husky", "pre-commit": "lint-staged", "lint": "eslint \"src/**/*.ts\"", "lint:fix": "eslint \"src/**/*.ts\" --fix", "format": "prettier --write \"src/**/*.ts\""}, "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"@fast-csv/format": "^4.3.5", "@google-analytics/data": "^3.2.2", "@google-cloud/speech": "^6.6.0", "@hubspot/api-client": "^8.5.0", "@socket.io/redis-adapter": "^7.2.0", "@socket.io/sticky": "^1.0.2", "@supercharge/promise-pool": "^3.2.0", "adm-zip": "^0.5.1", "axios": "1.7.4", "bee-queue": "^1.7.1", "bluebird": "^3.7.2", "body-parser": "^1.20.3", "clamscan": "^1.4.2", "class-transformer": "^0.3.1", "class-validator": "^0.14.0", "cls-hooked": "^4.2.2", "connect-redis": "^4.0.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "countries-and-timezones": "^2.2.0", "dotenv": "^8.2.0", "env-var": "^7.0.0", "error-stack-parser": "^2.1.4", "express": "^4.21.1", "express-session": "^1.18.1", "file-type": "^16.5.3", "google-auth-library": "10.1.0", "helmet": "^4.6.0", "image-size": "^1.1.1", "ip3country": "^5.0.0", "jose": "5.10.0", "lodash": "^4.17.20", "mailchimp-api-v3": "^1.13.1", "md5": "^2.2.1", "moment": "^2.26.0", "moment-timezone": "^0.5.31", "morgan": "^1.10.0", "mssql": "^10.0.2", "multer": "^1.4.5-lts.1", "password-hash": "^1.2.2", "pg": "^8.15.6", "postmark": "^4.0.0", "rate-limiter-flexible": "^2.2.2", "redis": "^3.1.2", "reflect-metadata": "^0.1.13", "saml2-js": "^4.0.2", "sequelize": "6.37.5", "shopify-api-node": "^3.12.3", "slugify": "^1.6.6", "socket.io": "^4.6.0", "source-map-support": "^0.5.20", "stripe": "^8.196.0", "swagger-ui-express": "^4.6.3", "timezones.json": "^1.6.1", "ts-node-dev": "^2.0.0", "twilio": "^4.19.3", "ua-parser-js": "^1.0.36", "uuidv4": "^6.0.8", "vimeo": "^2.1.1", "winston": "^3.17.0", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^15.0.0", "@cspell/eslint-plugin": "^6.31.1", "@types/bluebird": "^3.5.30", "@types/body-parser": "^1.19.0", "@types/chance": "^1.1.3", "@types/cls-hooked": "^4.3.8", "@types/dotenv": "^8.2.0", "@types/express": "^4.17.6", "@types/jest": "^29.5.3", "@types/lodash": "^4.14.167", "@types/morgan": "^1.9.9", "@types/mssql": "^9.1.5", "@types/multer": "^1.4.11", "@types/node": "^22.9.0", "@types/password-hash": "^1.2.20", "@types/redis": "^2.8.18", "@types/request": "^2.48.4", "@types/request-promise": "^4.1.46", "@types/saml2-js": "^3.0.1", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "chance": "^1.1.11", "commitlint-config-jira": "^1.5.2", "commitlint-plugin-jira-rules": "^1.6.4", "concurrently": "^5.2.0", "copyfiles": "^2.4.1", "cross-env": "^7.0.3", "eslint": "^8.45.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-security": "^3.0.1", "eslint-plugin-simple-import-sort": "10.0.0", "eslint-plugin-sonarjs": "^2.0.4", "eslint-plugin-unicorn": "^44.0.2", "husky": "^9.1.6", "jest": "29.6.1", "lint-staged": "^15.2.10", "nodemon": "^3.1.7", "prettier": "^2.8.1", "rimraf": "^3.0.2", "sequelize-cli": "^6.6.3", "ts-jest": "^29.1.1", "ts-node": "^10.2.1", "tsc-alias": "^1.8.10", "tsconfig-paths": "^3.12.0", "tsnd": "^1.1.0", "typescript": "^5.6.3"}}