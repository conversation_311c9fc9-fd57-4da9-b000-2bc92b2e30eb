module.exports = {
    root: true,
    env: {
        es2022: true,
        node: true,
    },
    plugins: ['@typescript-eslint/eslint-plugin', 'unicorn'],
    extends: [
        'airbnb-typescript/base',
        'plugin:unicorn/recommended',
        'plugin:@typescript-eslint/eslint-recommended',
        'eslint:recommended',
        'plugin:prettier/recommended',
        'plugin:@typescript-eslint/recommended',
        'prettier',
        'plugin:security/recommended-legacy',
        'plugin:sonarjs/recommended-legacy',
        // 'plugin:@cspell/recommended'
    ],
    rules: {
        'no-console': 'error',
        'no-unused-vars': 'error',
        'unicorn/expiring-todo-comments': 'off',
        'unicorn/prevent-abbreviations': 'off',
        'unicorn/no-process-exit': 'off',
        'unicorn/filename-case': 'off',
        'unicorn/prefer-spread': 'off',
        '@typescript-eslint/return-await': 'off',
        'sonarjs/prefer-nullish-coalescing': 'off',
        'security/detect-object-injection': 'off',
    },
    overrides: [
        {
            files: ['**/*.ts'],
            parser: '@typescript-eslint/parser',
            parserOptions: {
                project: 'tsconfig.json',
                sourceType: 'module',
                tsconfigRootDir: __dirname,
            },
            rules: {
                '@typescript-eslint/interface-name-prefix': 'off',
                '@typescript-eslint/no-explicit-any': 'off',
                '@typescript-eslint/explicit-module-boundary-types': 'off',
                '@typescript-eslint/no-unused-vars': 'off',
                '@typescript-eslint/ban-types': 'off',
                '@typescript-eslint/no-empty-interface': 'off',
                '@typescript-eslint/no-use-before-define': 'off',
                '@typescript-eslint/ter-arrow-parens': 'off',
                'unicorn/no-array-reduce': 'off',
                '@typescript-eslint/no-throw-literal': 'off',
                'unicorn/no-null': 'off',
                'unicorn/no-array-for-each': 'off',
            },
        }
    ],
};
